import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // Add import for HapticFeedback
import 'package:get/get.dart';

import 'dart:math' as math;
import 'dart:async';
import 'dart:convert';

import '../models/shape_data.dart';
import '../models/group_shape_data.dart';
import '../views/transformable_shape.dart';
import '../utils/history_manager.dart';
import '../painters/grid_system.dart';
import '../handlers/shape_manipulation_handlers.dart';
import '../utils/geometry_utils.dart'; // Add import for GeometryUtils

import '../widgets/professional_grid.dart';
import '../widgets/expandable_toolbar.dart';
import '../constants/grid_constants.dart';
import '../../new_item/controllers/new_item_wizard_controller.dart';
import '../../../data/models/shape_test_state_model.dart';

// Import all managers using the barrel file
import 'managers/index.dart';
import 'managers/snapping_manager.dart'; // <-- Add import
import 'managers/auto_pan_manager.dart'; // <-- Add auto-pan manager import
import '../widgets/custom_shape_creator.dart';

// Property display mode options
enum PropertyDisplayMode {
  none, // No property display
  badges, // Small badges near handles
  hud, // Floating HUD panel
  both // Both HUD and badges
}

enum InteractionType { vertex, edge, rotation, shapeDrag, none }

class ShapeEditorController extends GetxController {
  // Sub-managers - using nullable late variables to prevent re-initialization errors
  late final ShapeManager shapeManager;
  late final SelectionManager selectionManager;
  late final TransformationManager _transformationManager;
  late final GroupManager _groupManager;
  late final EnhancedMirrorModeManager _mirrorModeManager;
  late final UIStateManager _uiStateManager;
  late final ZoomManager _zoomManager;
  late final UndoRedoManager _undoRedoManager;
  late final KnittingInstructionsManager _knittingInstructionsManager;
  late final SnappingManager
      snappingManager; // <-- Add SnappingManager instance
  late final AutoPanManager _autoPanManager; // <-- Add AutoPanManager instance

  // Flag to track if managers have been initialized
  bool _managersInitialized = false;

  // Grid coordinate converter for device-independent positioning
  late GridCoordinateConverter gridCoordinateConverter;

  // Flag to track if we've already converted shapes to grid coordinates
  // This prevents duplicate conversions when loading saved shapes
  final RxBool hasConvertedToGridCoordinates = false.obs;

  // Reference to the toolbar for collapsing rows when clicking elsewhere
  final GlobalKey<ExpandableToolbarState> toolbarKey =
      GlobalKey<ExpandableToolbarState>();

  // GlobalKey for the MultiSelectPanel to prevent deselection when tapping on it
  final GlobalKey multiSelectPanelKey = GlobalKey();

  // Public getter to maintain backward compatibility
  RxList<TransformableShape> get shapes => shapeManager.shapes;
  RxList<int> get selectedIndices => selectionManager.selectedIndices;

  // Expose shape keys order for saving
  List<Key> get shapeKeysOrder => shapeManager.shapeKeysOrder;

  // Getter for knitting instructions manager
  KnittingInstructionsManager get knittingInstructionsManager =>
      _knittingInstructionsManager;

  // Grid labeling properties
  final RxBool showGridLabels = true.obs; // Control grid label visibility
  final RxBool isGridInteracting =
      false.obs; // Track if user is interacting with grid

  // Needle labeling property
  final RxBool showNeedleLabels = true.obs; // Control needle label visibility

  // NEW: Handle mode management
  final Rx<HandleMode> currentHandleMode = HandleMode.normal.obs;

  // Track which shape is in a specific mode (only one shape can be in sideEdit/pointEdit at a time)
  final Rx<Key?> activeHandleModeShapeKey = Rx<Key?>(null);

  // NEW: Active handle tracking for visual feedback
  final Rx<ActiveHandleInfo?> activeHandle = Rx<ActiveHandleInfo?>(null);

  // NEW: Active handle management methods
  void setActiveHandle(HandleType type, Key shapeKey,
      {int? index, int? subIndex}) {
    activeHandle.value = ActiveHandleInfo(
      type: type,
      shapeKey: shapeKey,
      index: index,
      subIndex: subIndex,
    );
  }

  void clearActiveHandle() {
    activeHandle.value = null;
  }

  bool isHandleActive(HandleType type, Key shapeKey,
      {int? index, int? subIndex}) {
    final current = activeHandle.value;
    if (current == null) return false;

    // Only show active handle visual feedback during point editing and edge editing modes
    // In normal selection mode, handles should not show active visual feedback
    if (currentHandleMode.value == HandleMode.normal) {
      return false;
    }

    return current.type == type &&
        current.shapeKey == shapeKey &&
        current.index == index &&
        current.subIndex == subIndex;
  }

  // UI related getters
  RxBool get isToolbarExpanded => _uiStateManager.isToolbarExpanded;
  RxBool get isShapeMenuOpen => _uiStateManager.isShapeMenuOpen;
  RxBool get isMultiSelecting => selectionManager.isMultiSelecting;
  bool get isMultiSelectionMode => selectionManager.isMultiSelectionMode.value;
  set isMultiSelectionMode(bool value) {
    // Prevent enabling multiselect mode when mirror mode is active
    if (value && isMirrorModeActive.value) {
      // Get.snackbar(
      //   'shapeEditor_mirrorMode_active'.tr,
      //   'shapeEditor_mirrorMode_multiSelectionNotAvailable'.tr,
      //   duration: const Duration(seconds: 2),
      //   snackPosition: SnackPosition.BOTTOM,
      // );
      return;
    }
    selectionManager.isMultiSelectionMode.value = value;
  }

  // Add snap-to-center feature toggle
  final RxBool snapToCenter = true.obs;

  // Property display mode with default setting
  final Rx<PropertyDisplayMode> propertyDisplayMode =
      PropertyDisplayMode.both.obs;

  // Toggle for showing cm values vs grid units
  final RxBool showMetricValues = true.obs;

  // Mirror mode related getters
  RxBool get isMirrorModeActive => _mirrorModeManager.isMirrorModeActive;
  Key? get activeMirrorModeShapeKey =>
      _mirrorModeManager.activeOriginalShapeKey.value;
  set activeMirrorModeShapeKey(Key? value) =>
      _mirrorModeManager.activeOriginalShapeKey.value = value;

  // Access to internal collections for backward compatibility
  Map<Key, Key> get mirroredPairs => _mirrorModeManager.mirroredPairs;
  Set<Key> get shapesCreatedInMirrorMode =>
      _mirrorModeManager.shapesCreatedInCurrentSession;
  RxMap<Key, ShapeData> get shapeStates => shapeManager.shapeStates;
  Map<Key, bool> get curveModeStates => _uiStateManager.curveModeStates;

  // Track the last added shape type for toolbar icon
  ShapeType? get lastAddedShapeType => shapeManager.lastAddedShapeType;

  // Drag and handle interaction flags
  bool get isValidDrag => selectionManager.isValidDrag;
  set isValidDrag(bool value) => selectionManager.isValidDrag = value;
  bool get isHandleInteraction => selectionManager.isHandleInteraction;
  set isHandleInteraction(bool value) =>
      selectionManager.isHandleInteraction = value;

  // Undo/redo state
  RxBool get canUndo => _undoRedoManager.canUndo;
  RxBool get canRedo => _undoRedoManager.canRedo;
  // Get the last operation name for user feedback
  Rx<String?> get lastOperation => _undoRedoManager.lastOperation;

  // Animation controller for undo/redo visual feedback
  RxBool isUndoRedoAnimating = false.obs;
  RxString animatingOperation = ''.obs;

  // Zoom related properties
  Rx<double> get zoomScale => _zoomManager.zoomScale;
  Rx<Offset> get panOffset => _zoomManager.panOffset;
  bool get isPanning => _zoomManager.isPanning.value;
  set isPanning(bool value) => _zoomManager.isPanning.value = value;

  // Grid system for professional grid rendering and snapping
  late GridSystem gridSystem;

  // Aspect ratio for grid rendering
  final RxDouble aspectRatio =
      1.0.obs; // Initialize, will be calculated from gauge

  // Gauge values
  final RxDouble stitchesPerCm = 1.0.obs;
  final RxDouble rowsPerCm = 1.0.obs;

  // Track snap indicators for visual feedback
  final RxList<SnapIndicator> snapIndicators = <SnapIndicator>[].obs;

  // Track modifier key states
  final RxBool isShiftKeyDown = false.obs;

  // Track the number of active touch pointers for multi-touch detection
  int activePointerCount = 0;

  // Timer for auto-saving
  Timer? _autoSaveTimer;

  // Store the initial state if loaded from the wizard
  HistoryEntry? _initialLoadedStateEntry;

  // Flag to prevent concurrent shape loading
  bool _isLoadingShapes = false;

  InteractionType currentInteractionType = InteractionType.none;
  int? interactingShapeIndex;
  int? interactingHandleIndex;

  final TransformationController transformationController =
      TransformationController();

  // Responsive HUD State
  var hudPosition =
      Rx<Offset?>(null); // null = docked, Offset = floating position
  final double hudBreakpoint = 600.0; // Width threshold for floating HUD
  var hudSize = Rx<Size>(const Size(300, 150)); // Store HUD dimensions

  // --- Snap State Management ---
  final Map<Key, dynamic> _currentSnapStates =
      {}; // Stores SnapInfo or 'centerSnap'
  final Map<Key, Offset> _pointerPositionAtSnap = {};
  final double _snapReleaseThreshold =
      10.0; // Pixels to move before releasing snap

  // --- Added for Snap Lines Visual Feedback ---
  final Rx<SnapInfo?> activeSnapInfo = Rx<SnapInfo?>(null);

  // --- Context Menu State ---
  final Rx<Offset?> initialContextMenuPosition =
      Rx<Offset?>(null); // Store the raw press position
  final Rx<Offset?> calculatedContextMenuPosition =
      Rx<Offset?>(null); // Position after bounds calculation
  final RxBool isContextMenuVisible = false.obs;
  final GlobalKey contextMenuKey =
      GlobalKey(); // Key for the context menu widget

  // --- Custom Shape Creator Key ---
  final GlobalKey customShapeCreatorKey =
      GlobalKey(); // Key for the CustomShapeCreator widget

  // --- Edit Mode Modal Keys ---
  final GlobalKey editSidesModalKey =
      GlobalKey(); // Key for the EditSidesModal widget
  final GlobalKey editPointsModalKey =
      GlobalKey(); // Key for the EditPointsModal widget

  static ShapeEditorController get to => Get.find();

  // --- Add method to update shift key state ---
  void updateShiftKeyState(bool isDown) {
    isShiftKeyDown.value = isDown;
  }
  // --- End Add ---

  // --- Add onDragStart logic (or integrate into existing gesture detector) ---
  // Need to capture the starting position and clear previous snap states
  void onDragStart(Offset globalPosition) {
    _currentSnapStates.clear();
    _pointerPositionAtSnap.clear();
    // Store the initial state of dragged shapes for history
    startHistoryTracking("Drag Shape${selectedIndices.length > 1 ? 's' : ''}");
  }

  // --- Add onDragEnd logic (or integrate into existing gesture detector) ---
  void onDragEnd() {
    // Stop auto-panning when drag ends
    _autoPanManager.stopAutoPan();

    // Finish history tracking only if changes occurred (implicitly handled by finishHistoryTracking)
    finishHistoryTracking(); // This will also save to wizard state
    _currentSnapStates.clear();
    _pointerPositionAtSnap.clear();
    activeSnapInfo.value = null; // Clear visual snap lines

    // Clear active handle state
    clearActiveHandle();
  }

  @override
  void onInit() {
    super.onInit();

    // Only initialize managers if they haven't been initialized yet
    if (!_managersInitialized) {
      debugPrint(
          "[ShapeEditorController] Initializing managers for the first time");
      // Setup sub-managers
      shapeManager = ShapeManager();
      selectionManager = SelectionManager(shapeManager);
      _transformationManager = TransformationManager(shapeManager);
      _groupManager = GroupManager(shapeManager, selectionManager);
      _mirrorModeManager =
          EnhancedMirrorModeManager(shapeManager, selectionManager, this);
      _uiStateManager = UIStateManager();
      _zoomManager = ZoomManager();
      _undoRedoManager = UndoRedoManager(
          useMemoryOptimization: true, enableDebugLogging: true);
      _knittingInstructionsManager = KnittingInstructionsManager(this);

      // Initialize AutoPanManager with pan update callback
      _autoPanManager = AutoPanManager(
        onPanUpdate: (Offset panDelta) {
          // Apply the pan delta to the current pan offset
          _zoomManager.applyPanDelta(panDelta);

          // Update the transformation controller to reflect the change
          _ensureTransformationControllerSync();

          // Update grid system
          gridSystem.updateViewport(
            zoomScale.value,
            panOffset.value,
            updateNeedleMapping:
                false, // Skip full remapping during auto-pan for performance
          );

          // Trigger UI update
          update();
        },
      );

      _managersInitialized = true;
    } else {
      debugPrint(
          "[ShapeEditorController] Managers already initialized, skipping initialization");
    }

    // Initialize UI state
    _uiStateManager.initialize();

    // Set up auto-save timer (save every 2 minutes)
    _autoSaveTimer = Timer.periodic(const Duration(minutes: 2), (_) {
      // Only auto-save if we have shapes
      if (shapeManager.shapes.isNotEmpty) {
        saveToWizardState();
      }
    });

    // Get the needle count and pitch from the wizard controller if available
    int? needleCount;
    double? needlePitch;
    try {
      if (Get.isRegistered<NewItemWizardController>()) {
        final wizardController = Get.find<NewItemWizardController>();
        final knittingMachine = wizardController.newItem.value.knittingMachine;
        final newItem = wizardController.newItem.value;

        needleCount = knittingMachine?.needlesCount;
        needlePitch = knittingMachine?.needlePitch;

        // Load gauge values
        stitchesPerCm.value = newItem.stitchesPerCm ?? 1.0;
        rowsPerCm.value = newItem.rowsPerCm ?? 1.0;

        // Calculate initial aspect ratio
        if (rowsPerCm.value > 0 && stitchesPerCm.value > 0) {
          aspectRatio.value = stitchesPerCm.value /
              rowsPerCm
                  .value; //represents how many stitches are equivalent to one row
        } else {
          aspectRatio.value = 1.0; // Avoid division by zero, default to 1:1
        }
      }
    } catch (e) {
      // Silently handle if we can't get the machine info or gauge

      aspectRatio.value = 1.0; // Default on error
      stitchesPerCm.value = 1.0;
      rowsPerCm.value = 1.0;
    }

    // Initialize the grid system with standard grid type
    gridSystem = GridSystem(
      cellWidth: 1.7,
      primaryColor: Colors.blue,
      gridColor: Colors.grey.shade300,
      opacity: 0.7,
      zoomLevel: zoomScale.value,
      panOffset: panOffset.value,
      needleCount: needleCount ?? 200, // Default to 200 needles if null
      snapToGrid: true,
      snapThreshold: 5.0,
      machinePitch:
          needlePitch ?? 5.0, // Pass the machine's needle pitch or default
      snapToCenter: snapToCenter.value, // Initialize snap-to-center
      showNeedleLabels: showNeedleLabels.value, // Initialize needle labels
      aspectRatio: aspectRatio.value, // Pass aspect ratio
    );

    // Initialize the grid coordinate converter
    gridCoordinateConverter = GridCoordinateConverter(
      screenSize: Size(Get.width, Get.height),
      needleCount: needleCount ?? 200, // Default to 200 needles if null
      aspectRatio: aspectRatio.value,
    );

    // --- Initialize SnappingManager AFTER GridSystem ---
    snappingManager = SnappingManager(gridSystem: gridSystem);
    // -------------------------------------------------

    // Ensure the GridSystem is immediately synchronized with current state
    gridSystem.updateViewport(zoomScale.value, panOffset.value,
        updateNeedleMapping: true);

    // CRITICAL FIX: Ensure transformation controller is properly initialized
    // This fixes the coordinate mismatch issue on initial load
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _ensureTransformationControllerSync();
    });

    // Listen for zoom and pan changes to update grid system
    ever(
        zoomScale,
        (_) => gridSystem.updateViewport(zoomScale.value, panOffset.value,
            updateNeedleMapping: true));
    ever(
        panOffset,
        (_) => gridSystem.updateViewport(zoomScale.value, panOffset.value,
            updateNeedleMapping: false));

    // Set grid interaction when panning/zooming
    ever(zoomScale, (_) => isGridInteracting.value = true);
    ever(panOffset, (_) => isGridInteracting.value = true);

    // Reset interaction status after a short delay
    ever(isGridInteracting, (value) {
      if (value) {
        Future.delayed(const Duration(milliseconds: 1500), () {
          isGridInteracting.value = false;
        });
      }
    });

    // Listen for snap-to-center changes
    ever(snapToCenter, (value) {
      gridSystem.snapToCenter = value;
    });

    // Listen for needle label visibility changes
    ever(showNeedleLabels,
        (value) => updateGridProperties(showNeedleLabels: value));

    // Note: We'll handle screen size changes by calling handleScreenSizeChange
    // from the view when needed, instead of trying to listen for size changes

    // Add listener to recalculate position when visibility changes
    ever(isContextMenuVisible, (visible) {
      if (visible && initialContextMenuPosition.value != null) {
        // Trigger calculation when menu becomes visible
        // Need context here - this might be tricky.
        // Let's rely on the post-frame callback in the widget for now.
      }
    });
  }

  /// Convert all shapes from grid coordinates to screen coordinates
  void _convertShapesToCurrentScreen() {
    // Only proceed if we've previously converted to grid coordinates
    if (!hasConvertedToGridCoordinates.value) return;

    // Process all shapes
    final convertedShapeStates = <Key, ShapeData>{};

    for (final entry in shapeManager.shapeStates.entries) {
      final key = entry.key;
      final shapeData = entry.value;

      // Convert the shape data to screen coordinates
      final convertedShape =
          shapeData.fromGridCoordinates(gridCoordinateConverter);
      convertedShapeStates[key] = convertedShape;
    }

    // Update the shape states with converted shapes
    shapeManager.shapeStates.clear();
    shapeManager.shapeStates.addAll(convertedShapeStates);

    // Rebuild all shapes with the new coordinates
    _rebuildShapesFromHistory();
  }

  // Zoom functionality methods
  void resetZoom() {
    _zoomManager.resetZoom();
    update();
  }

  // Method to calculate the bounding box of all shapes
  Rect? _calculateOverallBoundingBox() {
    if (shapeManager.shapeStates.isEmpty) {
      return null;
    }

    double minX = double.infinity;
    double minY = double.infinity;
    double maxX = -double.infinity;
    double maxY = -double.infinity;

    for (final shapeData in shapeManager.shapeStates.values) {
      // Use the shape's bounding box for calculation
      final rect = shapeData.boundingRect;
      minX = math.min(minX, rect.left);
      minY = math.min(minY, rect.top);
      maxX = math.max(maxX, rect.right);
      maxY = math.max(maxY, rect.bottom);
    }

    if (minX > maxX || minY > maxY) {
      // Should not happen if there are shapes, but handle defensively
      return null;
    }

    return Rect.fromLTRB(minX, minY, maxX, maxY);
  }

  // New method to reset zoom and center content
  void resetZoomAndCenterContent() {
    final Rect? overallBounds = _calculateOverallBoundingBox();
    final Size screenSize = Get.size; // Use Get.size for screen dimensions

    // Start with a default zoom that shows the full grid
    _zoomManager.zoomScale.value = 1.0;

    // More balanced padding values - enough for labels without being excessive
    const double horizontalPadding = 120.0; // Side label space
    const double topPadding = 100.0; // Top area space
    const double bottomPadding = 120.0; // Bottom label space

    // If no shapes, position at a standard location with good visibility
    if (overallBounds == null) {
      // Position grid with enough room for labels
      _zoomManager.updatePanOffset(Offset(horizontalPadding, topPadding));
      update();
      return;
    }

    // Expand the effective content bounds with reasonable padding
    final Rect expandedBounds = Rect.fromLTRB(
        overallBounds.left - horizontalPadding,
        overallBounds.top - topPadding,
        overallBounds.right + horizontalPadding,
        overallBounds.bottom + bottomPadding);

    // Calculate minimum zoom needed to fit everything horizontally
    final double contentWidth = expandedBounds.width;
    final double horizontalZoom =
        contentWidth > 0 ? screenSize.width / contentWidth : 1.0;

    // Calculate minimum zoom needed to fit everything vertically
    final double contentHeight = expandedBounds.height;
    final double verticalZoom =
        contentHeight > 0 ? screenSize.height / contentHeight : 1.0;

    // Use the smaller zoom with a moderate safety margin
    final double targetZoom = math.min(
        math.min(horizontalZoom, verticalZoom) * 0.85, // 15% safety margin
        1.0 // Never zoom in beyond 1.0 for auto-fit
        );

    // Update zoom scale - ensure zoom isn't too small to see content clearly
    // Use extended minimum zoom range for better overview capability
    final double clampedZoom =
        math.max(targetZoom, 0.01); // Minimum zoom of 0.01 (1%)
    _zoomManager.zoomScale.value = clampedZoom;

    // Center horizontally
    final double screenCenterX = screenSize.width / 2;
    final double contentCenterX = overallBounds.center.dx;
    final double targetPanX = screenCenterX - (contentCenterX * clampedZoom);

    // Position at top with appropriate padding
    final double targetPanY = topPadding - (overallBounds.top * clampedZoom);

    // Apply the calculated offset
    final Offset targetPanOffset = Offset(targetPanX, targetPanY);
    _zoomManager.updatePanOffset(targetPanOffset);

    // Ensure UI refreshes with the new zoom and pan
    update();
  }

  void updateZoom(double scale, Offset offset) {
    _zoomManager.updateZoom(scale, offset);
    update();
  }

  // Handle pan offset updates separately from zoom
  void updatePanOffset(Offset newOffset) {
    _zoomManager.updatePanOffset(newOffset);
    update();
  }

  // Calculate pan bounds based on current screen size, zoom level and grid size
  Offset calculatePanBounds(
      Size screenSize, Offset panOffset, double zoom, bool isMin) {
    return _zoomManager.calculatePanBounds(screenSize, panOffset, zoom, isMin);
  }

  // Add getters for binding to UI
  bool get hasUndo => _undoRedoManager.hasUndo;
  bool get hasRedo => _undoRedoManager.hasRedo;

  /// Get the operation name of the last action that can be undone
  String? getLastUndoOperationName() {
    return _undoRedoManager.getLastUndoOperationName();
  }

  /// Get the operation name of the next action that can be redone
  String? getNextRedoOperationName() {
    return _undoRedoManager.getNextRedoOperationName();
  }

  // Snap a point to the grid
  Offset snapToGrid(Offset point, Size size) {
    // In mirror mode, we strictly enforce the horizontal positioning
    if (isMirrorModeActive.value) {
      // Find which shape is being snapped (if any)
      Key? activeShapeKey;
      for (int i = 0; i < selectedIndices.length; i++) {
        final index = selectedIndices[i];
        if (index >= 0 && index < shapes.length) {
          activeShapeKey = shapes[index].key;
          break;
        }
      }

      // If we found a shape and it's part of a mirrored pair
      if (activeShapeKey != null &&
          (_mirrorModeManager.isShapeOriginal(activeShapeKey) ||
              _mirrorModeManager.isShapeMirrored(activeShapeKey))) {
        // For any point in mirror mode, keep X position fixed at center line
        // and only allow movement in Y direction
        // TODO: Refactor mirror mode snapping/constraints - might belong in SnappingManager or dedicated constraint system
        final snappedPoint =
            // gridSystem.snapPointToGrid(
            snappingManager.snapPoint(Offset(point.dx, point.dy),
                size); // <-- Still using GridSystem temporarily // Changed to snappingManager
        return Offset(
            point.dx, snappedPoint.dy); // Preserve original X, snap only Y
      }
    }

    // Default behavior for non-mirror mode
    // return gridSystem.snapPointToGrid(point, size); // <-- Replace with SnappingManager call
    return snappingManager.snapPoint(point, size);
  }

  /// Toggle between property display modes
  void togglePropertyDisplayMode() {
    // Simplify to toggle between HUD and none since the HUD is now docked at the bottom
    if (propertyDisplayMode.value == PropertyDisplayMode.none) {
      propertyDisplayMode.value = PropertyDisplayMode.hud;
    } else {
      propertyDisplayMode.value = PropertyDisplayMode.none;
    }
    update();
  }

  /// Toggle between showing metric values (cm) and grid units
  void toggleMetricValues() {
    showMetricValues.value = !showMetricValues.value;
    update();
  }

  /// Toggle grid labels visibility
  void toggleGridLabels() {
    showGridLabels.value = !showGridLabels.value;
    update();
  }

  /// Toggle needle labels visibility
  void toggleNeedleLabels() {
    showNeedleLabels.value = !showNeedleLabels.value;

    // Update the grid system
    gridSystem = GridSystem(
      cellWidth: gridSystem.cellWidth,
      primaryColor: gridSystem.primaryColor,
      gridColor: gridSystem.gridColor,
      opacity: gridSystem.opacity,
      zoomLevel: gridSystem.zoomLevel,
      panOffset: gridSystem.panOffset,
      needleCount: gridSystem.needleCount,
      showCenterLines: gridSystem.showCenterLines,
      snapToGrid: gridSystem.snapToGrid,
      snapToCenter: gridSystem.snapToCenter,
      snapThreshold: gridSystem.snapThreshold,
      centerSnapThreshold: gridSystem.centerSnapThreshold,
      machinePitch: gridSystem.machinePitch,
      showNeedleLabels: showNeedleLabels.value,
    );
    update();
  }

  /// Set grid interaction state - used to control label opacity
  void setGridInteracting(bool interacting) {
    isGridInteracting.value = interacting;
  }

  /// Public method to reload shapes from wizard state
  /// This can be called when returning to the shape editor
  /// Accepts an optional callback that will be called when loading is complete
  Future<void> reloadShapesFromWizardState({Function? onComplete}) async {
    // --- Loading Guard ---
    if (_isLoadingShapes) {
      debugPrint(
          "[ShapeEditorController] Shape reload already in progress. Skipping.");
      onComplete?.call(); // Call complete immediately if skipped
      return;
    }
    _isLoadingShapes = true;
    // ---------------------

    _initialLoadedStateEntry = null; // Reset initial state before loading
    try {
      // Clear current state first (do this INSIDE the guarded block)
      debugPrint(
          "[ShapeEditorController] Clearing current shape state before reload...");
      shapeManager.shapes.clear();
      shapeManager.shapeStates.clear();
      shapeManager.shapeKeysOrder.clear();
      _uiStateManager.curveModeStates.clear();
      selectedIndices.clear();

      if (Get.isRegistered<NewItemWizardController>()) {
        final wizardController = Get.find<NewItemWizardController>();
        if (wizardController.currentStateId.value != null) {
          final state = await wizardController.wizardStateService
              .loadWizardState(wizardController.currentStateId.value!);

          if (state?.shapeTestState?.shapes != null &&
              state!.shapeTestState!.shapes.isNotEmpty) {
            final savedShapes = state.shapeTestState!.shapes;
            debugPrint(
                "[ShapeEditorController] Found ${savedShapes.length} shapes in saved state.");

            // Prepare maps to hold the loaded state
            final Map<Key, ShapeData> loadedShapeStates = {};
            final List<Key> loadedShapeOrder = [];
            final Map<Key, bool> loadedCurveModes = {};
            // Assume selection is empty when loading
            final List<int> loadedSelectedIndices = [];

            gridCoordinateConverter = GridCoordinateConverter(
              screenSize: Size(Get.width, Get.height),
              needleCount: gridSystem.needleCount,
              aspectRatio: aspectRatio.value,
            );

            for (final shapeData in savedShapes) {
              final shapeKey = GlobalKey();
              ShapeData screenShapeData = shapeData;
              if (shapeData.gridVertices != null &&
                  shapeData.gridCenter != null) {
                screenShapeData =
                    shapeData.fromGridCoordinates(gridCoordinateConverter);
                hasConvertedToGridCoordinates.value = true;
              } else {
                screenShapeData =
                    shapeData.toGridCoordinates(gridCoordinateConverter);
                hasConvertedToGridCoordinates.value = true;
              }

              // Populate the state maps
              loadedShapeStates[shapeKey] = screenShapeData;
              loadedShapeOrder.add(shapeKey);
              loadedCurveModes[shapeKey] = false; // Default curve mode off

              final extendedConstraints = BoxConstraints(
                maxWidth: Get.width,
                maxHeight: GridConstants.getExtendedHeight(Get.height),
              );
              final shape = TransformableShape(
                key: shapeKey,
                constraints: extendedConstraints,
                initialShapeType: shapeData.type,
                initialShapeData: screenShapeData,
                selected: false, // Shapes are not selected on load
              );
              shapeManager.shapes.add(shape);
            }

            // Update the controller's state maps
            shapeManager.shapeStates.addAll(loadedShapeStates);
            shapeManager.shapeKeysOrder.addAll(loadedShapeOrder);
            _uiStateManager.curveModeStates.addAll(loadedCurveModes);
            // selectedIndices remains empty

            // --- Capture the loaded state as the initial state ---
            _initialLoadedStateEntry = HistoryEntry(
              shapeStates: Map.from(loadedShapeStates), // Deep copy crucial?
              shapeOrder: List.from(loadedShapeOrder),
              selectedIndices: List.from(loadedSelectedIndices),
              curveModeStates: Map.from(loadedCurveModes),
              operationName: "Loaded State",
            );
            debugPrint(
                "[ShapeEditorController] Captured initial loaded state with ${_initialLoadedStateEntry?.shapeStates.length} shapes.");
            // --------------------------------------------------------

            debugPrint(
                '[ShapeEditorController] Loaded ${savedShapes.length} shapes. Clearing history AFTER capture.');
            _undoRedoManager
                .clearHistory(); // Clear history AFTER capturing initial state
            update(); // Refresh UI
          } else {
            debugPrint(
                "[ShapeEditorController] No shapes found in saved state or state is null. Clearing history.");
            _initialLoadedStateEntry = null; // Ensure no initial state
            _undoRedoManager.clearHistory();
            _resetToInitialEmptyState();
          }
        } else {
          debugPrint(
              "[ShapeEditorController] No state ID found. Clearing history.");
          _initialLoadedStateEntry = null;
          _undoRedoManager.clearHistory();
          _resetToInitialEmptyState();
        }
      } else {
        debugPrint(
            "[ShapeEditorController] WizardController not registered. Clearing history.");
        _initialLoadedStateEntry = null;
        _undoRedoManager.clearHistory();
        _resetToInitialEmptyState();
      }
      onComplete?.call();
    } catch (e, stackTrace) {
      debugPrint(
          '[ShapeEditorController] Error loading shapes from wizard state: $e');
      debugPrint('Stack trace: $stackTrace');
      _initialLoadedStateEntry = null;
      _undoRedoManager.clearHistory(); // Ensure history is clear on error
      _resetToInitialEmptyState(); // Reset on error
      onComplete?.call();
    }
    debugPrint("[ShapeEditorController] Finished reloading shapes.");
    // --- Release Loading Guard ---
    _isLoadingShapes = false;
    // ---------------------------
  }

  /// Ensure transformation controller is properly synchronized with grid system
  /// This fixes coordinate mismatch issues on initial load
  void _ensureTransformationControllerSync() {
    // Force synchronization of the transformation controller with current zoom/pan values
    final matrix = Matrix4.identity()
      ..translate(panOffset.value.dx, panOffset.value.dy)
      ..scale(zoomScale.value);

    transformationController.value = matrix;

    // Ensure grid system is also updated with the same values
    gridSystem.updateViewport(zoomScale.value, panOffset.value,
        updateNeedleMapping: true);

    debugPrint(
        '[ShapeEditorController] Transformation controller synchronized: '
        'zoom=${zoomScale.value}, pan=${panOffset.value}');
  }

  /// CRITICAL FIX: Force complete grid system synchronization
  /// This ensures coordinate transformations work correctly at all zoom levels
  void ensureGridSystemSync() {
    // Update grid system with current zoom and pan values
    gridSystem.updateViewport(
      zoomScale.value,
      panOffset.value,
      updateNeedleMapping: true, // Force complete synchronization
    );

    // Also ensure transformation controller is in sync
    final matrix = Matrix4.identity()
      ..translate(panOffset.value.dx, panOffset.value.dy)
      ..scale(zoomScale.value);

    if (transformationController.value != matrix) {
      transformationController.value = matrix;
    }

    debugPrint(
        '[ShapeEditorController] Grid system synchronized for tap detection: '
        'zoom=${zoomScale.value}, pan=${panOffset.value}');
  }

  /// CRITICAL FIX: Transform screen coordinates to grid coordinates using the exact same
  /// transformation matrix as the InteractiveViewer for perfect alignment
  Offset screenToGridPoint(Offset screenPoint, Size screenSize) {
    // Get the current transformation matrix (same as InteractiveViewer uses)
    final matrix = transformationController.value;

    // Create the inverse transformation matrix
    final inverseMatrix = Matrix4.copy(matrix);
    inverseMatrix.invert();

    // Transform the screen point using the inverse matrix
    final transformedPoint =
        MatrixUtils.transformPoint(inverseMatrix, screenPoint);

    return transformedPoint;
  }

  /// Call this method when screen size changes to update positions
  void handleScreenSizeChange() {
    // Check if screen size actually changed
    final newSize = Size(Get.width, Get.height);

    // Update grid coordinate converter with new screen size
    gridCoordinateConverter = GridCoordinateConverter(
      screenSize: newSize,
      needleCount: gridSystem.needleCount,
      aspectRatio: aspectRatio.value,
    );

    // Convert all shapes to the new screen size
    if (hasConvertedToGridCoordinates.value) {
      _convertShapesToCurrentScreen();
    }

    // Update grid system with new zoom/pan values
    gridSystem.updateViewport(zoomScale.value, panOffset.value,
        updateNeedleMapping: true);

    // Reset zoom/pan to show all content if needed
    resetZoomAndCenterContent();

    update();
  }

  /// Check if curve mode is active for the currently selected shape
  bool isCurveModeActive() {
    if (selectedIndices.isEmpty) return false;

    final selectedIndex = selectedIndices.first;
    if (selectedIndex < 0 || selectedIndex >= shapes.length) return false;

    final selectedShape = shapes[selectedIndex];
    final shapeKey = selectedShape.key;

    if (shapeKey == null) return false;

    return _uiStateManager.isCurveModeActive(shapeKey);
  }

  /// Convert all shapes to grid coordinates
  /// This is called before saving or when needed to ensure device independence
  void _convertShapesToGridCoordinates() {
    // Skip if we've already converted
    if (hasConvertedToGridCoordinates.value) return;

    final convertedShapeStates = <Key, ShapeData>{};

    for (final entry in shapeManager.shapeStates.entries) {
      final key = entry.key;
      final shapeData = entry.value;

      // Convert the shape data to grid coordinates
      final convertedShape =
          shapeData.toGridCoordinates(gridCoordinateConverter);
      convertedShapeStates[key] = convertedShape;
    }

    // Update the shape states with converted shapes
    shapeManager.shapeStates.clear();
    shapeManager.shapeStates.addAll(convertedShapeStates);

    // Mark that we've converted to grid coordinates
    hasConvertedToGridCoordinates.value = true;
  }

  /// Calculate appropriate grid size based on machine needle pitch
  double calculateGridSizeFromNeedlePitch(double? needlePitch) {
    // Default grid size if no needle pitch available
    if (needlePitch == null) return 20.0;

    // Calculate grid size so that at zoom level 1.0:
    // 1 needle width = 1 grid cell
    // Convert mm to pixels at a reasonable scale (needlePitch is in mm)
    return needlePitch * 2.0; // Adjust multiplier as needed for visual clarity
  }

  // Save the shape state to allow persistence and mirroring
  void saveShapeState(Key shapeKey, ShapeData shapeData) {
    // Only mark shapes as modified if we're not currently loading shapes
    // This ensures we only track actual user modifications, not loading operations
    if (!_isLoadingShapes && Get.isRegistered<NewItemWizardController>()) {
      Get.find<NewItemWizardController>().markShapesAsModified();
    }

    // Keep original input for reference if needed, but work with a mutable copy for adjustments
    ShapeData currentShapeData = shapeData;

    // Check if we're in mirror mode to enforce constraints on curve controls
    if (isMirrorModeActive.value) {
      final screenCenter = Get.width / 2;
      bool isOriginalShape = !_mirrorModeManager.isShapeMirrored(shapeKey);

      Map<int, Offset> constrainedControls =
          Map.from(currentShapeData.curveControls);
      bool controlsChanged = false;

      for (final entry in currentShapeData.curveControls.entries) {
        if (entry.value != Offset.zero) {
          final controlIndex = entry.key;
          final controlOffset = entry.value;
          final v1 = currentShapeData.vertices[controlIndex];
          final v2 = currentShapeData
              .vertices[(controlIndex + 1) % currentShapeData.vertices.length];
          final midpoint = Offset((v1.dx + v2.dx) / 2, (v1.dy + v2.dy) / 2);
          final controlPoint = midpoint + controlOffset;

          if (isOriginalShape) {
            if (controlPoint.dx < screenCenter) {
              final newOffsetX = screenCenter - midpoint.dx;
              constrainedControls[controlIndex] =
                  Offset(newOffsetX, controlOffset.dy);
              controlsChanged = true;
            }
          } else {
            if (controlPoint.dx > screenCenter) {
              final newOffsetX = screenCenter - midpoint.dx;
              constrainedControls[controlIndex] =
                  Offset(newOffsetX, controlOffset.dy);
              controlsChanged = true;
            }
          }
        }
      }

      // Apply the constrained controls if changes were made
      if (controlsChanged) {
        currentShapeData =
            currentShapeData.copyWith(curveControls: constrainedControls);
      }
    }

    // Convert to grid coordinates before storing if needed
    // Important: Do this conversion *before* the mirror logic potentially modifies screen coordinates further
    final ShapeData dataToStoreInitially = hasConvertedToGridCoordinates.value
        ? currentShapeData.toGridCoordinates(gridCoordinateConverter)
        : currentShapeData;

    // If not in mirror mode, simply save and return
    if (!isMirrorModeActive.value) {
      shapeManager.saveShapeState(shapeKey, dataToStoreInitially);
      return;
    }

    // --- Mirror Mode Logic ---
    // Check if this is a mirrored shape
    if (_mirrorModeManager.isShapeMirrored(shapeKey)) {
      // If this is a mirrored shape, we shouldn't be directly modifying it
      // Find the original shape and update it instead
      final originalKey = _mirrorModeManager.getMirroredPair(shapeKey);
      if (originalKey != null) {
        // Get the current original shape data
        final originalData = shapeManager.shapeStates[originalKey];
        if (originalData != null) {
          // Save the original shape data (this will trigger the mirror update)
          shapeManager.saveShapeState(originalKey, originalData);
          // Let the mirror mode manager handle updating the mirrored shape
          _mirrorModeManager.handleShapeTransformation(originalKey);
        }
      }
      return;
    } else if (_mirrorModeManager.isShapeOriginal(shapeKey)) {
      // This is an original shape with a mirror
      // Save the shape state
      shapeManager.saveShapeState(shapeKey, dataToStoreInitially);
      // Let the mirror mode manager handle updating the mirrored shape
      _mirrorModeManager.handleShapeTransformation(shapeKey);
      return;
    }

    // If this shape doesn't have a mirror, just save it normally
    shapeManager.saveShapeState(shapeKey, dataToStoreInitially);
    _updateShapeWidget(shapeKey, dataToStoreInitially);
    return;
  }

  // Helper function to schedule widget update using the latest data from the manager
  void _updateShapeWidget(Key key, ShapeData storedData) {
    int index = shapes.indexWhere((s) => s.key == key);
    if (index >= 0) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        // Ensure the widget at the index still corresponds to the key
        if (index < shapes.length && shapes[index].key == key) {
          // Get the *absolute latest* data from the manager, as state might change rapidly
          final latestData = shapeManager.shapeStates[key];
          if (latestData == null) {
            debugPrint(
                "Warning: Shape state unexpectedly missing for key $key during widget update.");
            return;
          }

          // Convert latest data to screen coordinates for the widget
          final screenData = hasConvertedToGridCoordinates.value
              ? latestData.fromGridCoordinates(gridCoordinateConverter)
              : latestData;

          // Determine selection state based on selectedIndices
          final bool isSelected = selectedIndices.contains(index);

          // Check if curve mode is active for this shape key
          final bool curveMode = _uiStateManager.isCurveModeActive(key);

          // Recreate the shape widget with the latest data
          final updatedShape = TransformableShape(
            key: key,
            constraints: shapes[index].constraints, // Keep existing constraints
            initialShapeType:
                shapes[index].initialShapeType, // Keep original type
            initialShapeData: screenData, // Use latest screen data
            selected: isSelected, // Use current selection state
            initialCurveMode: curveMode, // Use current curve mode state
          );

          // Replace the shape in the list only if it's different (basic check)
          // This check might be too simple, but prevents unnecessary updates if data is identical
          // Compare the essential properties that determine if a widget rebuild is needed
          if (shapes[index].initialShapeData != screenData ||
              shapes[index].selected != isSelected ||
              shapes[index].initialCurveMode != curveMode) {
            // Compare against the existing widget's initialCurveMode
            shapes[index] = updatedShape;
            // Trigger a UI update for the GetX controller
            update();
          }
        }
      });
    }
  }

  // Method to get the current state of a shape (returns screen coordinates)
  ShapeData? getShapeState(Key? key) {
    if (key == null) return null;

    final storedShapeData = shapeManager.getShapeState(key);
    if (storedShapeData == null) return null;

    // If we've converted to grid coordinates, convert back to screen coordinates
    // for any consumer of this method
    if (hasConvertedToGridCoordinates.value) {
      return storedShapeData.fromGridCoordinates(gridCoordinateConverter);
    }

    return storedShapeData;
  }

  /// Method to get the internal state of a shape, preserving grid coordinates if set.
  /// Used primarily for saving the state accurately.
  ShapeData? getInternalShapeState(Key? key) {
    if (key == null) return null;
    return shapeManager.shapeStates[key]; // Directly return the stored data
  }

  // Handle a tap event in the view
  // Returns true if a shape was hit, false otherwise
  bool handleTapEvent(
      BuildContext context, // Keep context for potential future use
      Offset tapGridPosition, // Position in the grid coordinate system
      {required Offset
          globalTapPosition, // The original tap position in global coordinates
      bool isShiftDown = false,
      bool isLongPress = false}) {
    // If it's a long press, show context menu if over a shape or selection
    // (We might need more sophisticated logic here later to check if the long press
    // actually hit a shape, but for now, let's assume it did if there's a selection)
    if (isLongPress && selectedIndices.isNotEmpty) {
      showContextMenu(globalTapPosition); // Show menu at the tap position
      return true; // We handled the event by showing the menu
    }

    // --- Check if tap is inside the context menu ---
    // Use the original global tap position for this check
    if (isContextMenuVisible.value &&
        isTapInsideContextMenu(globalTapPosition)) {
      // Tap is inside the menu, let the menu handle it. Do nothing here.
      // The menu items themselves call controller methods and onDismiss.
      return true; // Indicate the tap was handled (by the menu)
    }

    // --- If tap was outside menu, hide it ---
    // This now correctly happens only when tapping *outside* the menu bounds
    if (isContextMenuVisible.value) {
      hideContextMenu();
      // Even if we hide the menu, we might still want to process the tap
      // e.g., deselect shapes if the tap was on the canvas background.
      // So, don't return here immediately.
    }

    // CRITICAL FIX: If we're already in a handle interaction, immediately return without doing anything else
    if (isHandleInteraction) {
      return true; // Return true to indicate we're interacting with a shape/handle
    }

    // --- Handle vertex edit mode taps ---
    if (isVertexEditModeActive.value) {
      // If no shapes are selected, allow normal shape selection to proceed
      if (selectedIndices.isEmpty) {
        // Continue with normal shape selection logic below
      } else {
        // We have selected shapes, handle vertex editing
        final hitVertexIndex = getHitVertexIndex(tapGridPosition);

        if (hitVertexIndex != -1) {
          // Hit a vertex, select it for editing
          selectedVertexIndex.value = hitVertexIndex;
          isDraggingVertex.value = false;
          return true;
        } else {
          // Check if we hit a different shape to select it
          // If we didn't hit any vertex, allow shape selection to proceed
          // Don't return true here, let the normal logic handle it
        }
      }
    }

    // Collapse any expanded toolbar rows when tapping on the board or shapes
    _collapseToolbarRows();

    // Track if any shape was hit
    bool hitShape = false;

    // Prevent multi-selection in mirror mode
    bool attemptingMultiSelection = isShiftDown || isMultiSelectionMode;
    if (isMirrorModeActive.value && attemptingMultiSelection) {
      // If already using multi-selection mode, show a notification
      if (isMultiSelectionMode) {
        // Get.snackbar(
        //   'shapeEditor_mirrorMode_active'.tr,
        //   'shapeEditor_mirrorMode_multiSelectionNotAvailable'.tr,
        //   duration: const Duration(seconds: 2),
        //   snackPosition: SnackPosition.BOTTOM,
        // );
      }
      // If using shift key, silently ignore multi-selection
      selectionManager.isMultiSelecting.value = false;
    } else {
      // Set multi-selecting mode based on EITHER shift key OR persistent multi-selection mode
      // This ensures multi-selection works in both desktop (shift key) and mobile (persistent mode) scenarios
      selectionManager.isMultiSelecting.value =
          isShiftDown || isMultiSelectionMode;
    }

    // Use a temporary list to avoid modifying shapes during iteration
    List<TransformableShape> shapeList = List.from(shapes);

    // First gather all shape states so we can check against paths
    for (int i = shapeList.length - 1; i >= 0; i--) {
      final shape = shapeList[i];

      // Get the key
      final Key? shapeKey = shape.key;
      if (shapeKey == null) continue;

      // Get the shape data
      final shapeData = shapeManager.shapeStates[shapeKey];
      if (shapeData == null) continue;

      // In mirror mode, we have additional restrictions on which shapes can be selected
      // TODO: Review mirror mode selection restriction logic if needed
      // if (isMirrorModeActive.value && !_mirrorModeManager.canSelectShape(shapeKey)) continue;

      // Get paths to test (assuming this path is in the shape's local coordinates)
      final path = selectionManager.createHitTestPath(shapeData);

      // Calculate the shape's full transformation matrix relative to the grid
      // (Rotation around its center + Translation of its center)
      final Matrix4 shapeTransform = Matrix4.identity()
        ..translate(shapeData.center.dx, shapeData.center.dy)
        ..rotateZ(shapeData.rotation)
        ..translate(
            -shapeData.center.dx,
            -shapeData.center
                .dy); // Matrix to transform shape's local coords to grid coords

      // Transform the path to its position/orientation on the grid
      final Path transformedPath = path.transform(shapeTransform.storage);

      // Check if the grid tap position is inside the transformed path
      if (transformedPath.contains(tapGridPosition)) {
        hitShape = true;
        // Found a hit!
        if (selectionManager.isMultiSelecting.value) {
          // Add to or remove from selection
          selectionManager.toggleShapeSelection(
              i, !selectedIndices.contains(i));
        } else {
          // Single selection mode, select only this shape
          selectionManager.selectShape(shape);
        }

        // Clear vertex selection when selecting a new shape in vertex edit mode
        if (isVertexEditModeActive.value) {
          selectedVertexIndex.value = -1;
          isDraggingVertex.value = false;
          _editingShapeKey = null;
        }

        break; // Stop after finding the first hit
      }
    }

    // NEW: Handle mode transitions based on tap results
    if (!hitShape) {
      // Tapped on background - exit any active handle modes and deselect shapes
      if (currentHandleMode.value != HandleMode.normal) {
        exitCurrentHandleMode();
      }

      if (isVertexEditModeActive.value) {
        // In vertex edit mode, don't deselect shapes, just deselect vertices
        selectedVertexIndex.value = -1;
        isDraggingVertex.value = false;
        _editingShapeKey = null;
      } else {
        // Normal mode: deselect all shapes
        debugPrint(
            '[ShapeEditorController] Deselecting all shapes (tap on background)');
        selectionManager.deselectAllShapes();
      }
    } else {
      // Hit a shape - check if we need to exit handle modes
      final hitShapeKey = shapeList
          .firstWhere(
              (s) => selectedIndices.any((index) => shapes[index].key == s.key))
          .key;

      // If we hit a different shape than the one in active mode, exit the mode
      if (currentHandleMode.value != HandleMode.normal &&
          activeHandleModeShapeKey.value != hitShapeKey) {
        exitCurrentHandleMode();
      }
    }

    // If we didn't hit any shape, deselect all shapes
    if (!hitShape && !isHandleInteraction) {
      if (isVertexEditModeActive.value) {
        // In vertex edit mode, don't deselect shapes, just deselect vertices
        selectedVertexIndex.value = -1;
        isDraggingVertex.value = false;
        _editingShapeKey = null;
      } else {
        // Normal mode: deselect all shapes
        debugPrint(
            '[ShapeEditorController] Deselecting all shapes (tap on background)');
        selectionManager.deselectAllShapes();
      }
    }

    // Only exit multi-selection mode if shift is not down AND we're not in a persistent multi-selection mode
    if (!isShiftDown && !isMultiSelectionMode) {
      selectionManager.isMultiSelecting.value = false;
    }

    // Return whether we hit a shape or not
    return hitShape;
  }

  /// Delete the currently selected shapes from the board
  void deleteSelectedShapes() {
    if (selectedIndices.isEmpty) return;

    final keysToDelete = <Key>[];
    final indicesToDelete = selectedIndices.toList()
      ..sort((a, b) => b.compareTo(a));

    for (final index in indicesToDelete) {
      if (index >= 0 && index < shapes.length) {
        final key = shapes[index].key;
        if (key != null) {
          keysToDelete.add(key);
        }
      }
    }

    // Check if any of the shapes being deleted are currently in an active editing mode
    bool shouldExitEditingModes = false;

    // Check if any shape being deleted is in handle mode (pointEdit or sideEdit)
    if (currentHandleMode.value != HandleMode.normal &&
        activeHandleModeShapeKey.value != null) {
      if (keysToDelete.contains(activeHandleModeShapeKey.value)) {
        shouldExitEditingModes = true;
      }
    }

    // Check if any shape being deleted is in vertex edit mode
    if (isVertexEditModeActive.value && _editingShapeKey != null) {
      if (keysToDelete.contains(_editingShapeKey)) {
        shouldExitEditingModes = true;
      }
    }

    // Exit editing modes immediately after deletion is confirmed but before UI updates
    if (shouldExitEditingModes) {
      debugPrint(
          '[ShapeEditorController] Exiting editing modes due to shape deletion');

      // Exit handle modes (pointEdit/sideEdit)
      if (currentHandleMode.value != HandleMode.normal) {
        exitCurrentHandleMode();
      }

      // Exit vertex edit mode
      if (isVertexEditModeActive.value) {
        isVertexEditModeActive.value = false;
        selectedVertexIndex.value = -1;
        isDraggingVertex.value = false;
        _editingShapeKey = null;
        _vertexDragStart = null;
        _vertexPointerStart = null;
      }
    }

    // Additional keys that need to be deleted (mirrored pairs)
    final additionalKeysToDelete = <Key>[];

    // Perform deletion operations
    for (final key in keysToDelete) {
      // Handle mirror mode deletion if applicable
      if (isMirrorModeActive.value) {
        // Let the mirror mode manager handle the deletion and get any paired key to delete
        final pairedKey = _mirrorModeManager.handleShapeDeletion(key);
        if (pairedKey != null && !keysToDelete.contains(pairedKey)) {
          additionalKeysToDelete.add(pairedKey);
        }
      }

      int shapeIndex = shapes.indexWhere((s) => s.key == key);
      if (shapeIndex != -1) shapes.removeAt(shapeIndex);

      shapeManager.shapeStates.remove(key);
      shapeManager.shapeKeysOrder.remove(key);
      _uiStateManager.curveModeStates.remove(key);
    }

    // Delete any additional shapes (mirrored pairs) that weren't in the original selection
    for (final key in additionalKeysToDelete) {
      int shapeIndex = shapes.indexWhere((s) => s.key == key);
      if (shapeIndex != -1) shapes.removeAt(shapeIndex);
    }

    selectedIndices.clear();

    // --- ADD HISTORY ENTRY HERE ---
    final operationName =
        "Delete Shape${indicesToDelete.length > 1 ? 's' : ''}";
    debugPrint(
        "[ShapeEditorController] Adding history entry AFTER state update: $operationName");
    _addHistoryEntry(operationName);
    // -----------------------------

    update();
    debugPrint(
        "[ShapeEditorController] Deleted selected shapes and their mirrors. Original keys: $keysToDelete, Additional keys: $additionalKeysToDelete");
  }

  // For backward compatibility (already calls the correct method)
  void deleteSelectedShape() {
    deleteSelectedShapes();
  }

  void addShape(ShapeType type) {
    if (isMirrorModeActive.value &&
        _mirrorModeManager.activeOriginalShapeKey.value != null) {
      // Get.snackbar('Mirror Mode', 'Only one shape allowed in mirror mode.');
      return;
    }

    BoxConstraints constraints = BoxConstraints(
        maxWidth: Get.width,
        maxHeight: GridConstants.getExtendedHeight(Get.height));
    final shape = shapeManager.addShape(type,
        constraints: constraints,
        screenWidth: Get.width,
        placeAtCenter: !isMirrorModeActive.value,
        currentZoom: zoomScale.value,
        currentPanOffset: panOffset.value);
    final shapeKey = shape.key;
    if (shapeKey == null) return;

    if (hasConvertedToGridCoordinates.value) {
      final shapeData = shapeManager.shapeStates[shapeKey];
      if (shapeData != null) {
        shapeManager.shapeStates[shapeKey] =
            shapeData.toGridCoordinates(gridCoordinateConverter);
      }
    }
    // Initialize curve mode state for new shape
    _uiStateManager.curveModeStates[shapeKey] = false;

    if (isMirrorModeActive.value) {
      // Get the initial shape data
      final initialShapeData = shapeManager.shapeStates[shapeKey];
      if (initialShapeData == null) return;

      // Convert to screen coordinates if needed
      final screenInitialShapeData = hasConvertedToGridCoordinates.value
          ? initialShapeData.fromGridCoordinates(gridCoordinateConverter)
          : initialShapeData;

      // Apply mirror mode constraints to the original shape to ensure it's positioned correctly
      // Specifically, make sure the leftmost vertex is exactly at the center line
      final updatedData = _mirrorModeManager.applyOriginalShapeConstraints(
          shapeKey, screenInitialShapeData);

      // Immediately update the visual representation of the original shape
      final constrainedShape = TransformableShape(
        key: shapeKey,
        constraints: constraints,
        initialShapeType: type,
        initialShapeData: updatedData,
        selected: true,
      );

      // Replace the shape in the list
      final shapeIndex = shapes.indexWhere((s) => s.key == shapeKey);
      if (shapeIndex >= 0) {
        shapes[shapeIndex] = constrainedShape;
      }

      // Create extended constraints for the mirrored shape
      final extendedConstraints = BoxConstraints(
          maxWidth: Get.width,
          maxHeight: GridConstants.getExtendedHeight(Get.height));

      // Use the enhanced mirror mode manager to create the mirrored shape
      // This uses the already constrained shape data
      final mirrorKey = _mirrorModeManager.createMirroredShape(
          shapeKey, updatedData, extendedConstraints);

      // Initialize curve mode for the mirrored shape
      _uiStateManager.curveModeStates[mirrorKey] = false;
    } else {
      // For non-mirror mode, snap the shape to horizontal center of the grid
      snapNewShapeToCenter(shapeKey);
    }

    selectedIndices.clear();
    int newIndex = shapes.indexWhere((s) => s.key == shapeKey);
    if (newIndex != -1) selectedIndices.add(newIndex);
    selectionManager.updateShapeSelectionState(
        curveModeStates: _uiStateManager.curveModeStates);

    // --- ADD HISTORY ENTRY HERE (AFTER state updates) ---
    final operationName = "Add ${type.toString().split('.').last} Shape";
    debugPrint(
        "[ShapeEditorController] Adding history entry AFTER state update: $operationName");
    _addHistoryEntry(operationName);
    // -----------------------------------------------------

    WidgetsBinding.instance.addPostFrameCallback((_) => update());
    debugPrint("[ShapeEditorController] Added shape with key: $shapeKey");
  }

  // New method to horizontally center a newly added shape
  void snapNewShapeToCenter(Key shapeKey) {
    try {
      // Get current shape data
      final currentData = getShapeState(shapeKey)?.deepCopy();
      if (currentData == null) return;

      // Calculate accurate bounding box
      final dynamic boundsResult =
          GeometryUtils.calculateAccurateBoundingRect(currentData);
      Rect currentRect;
      if (boundsResult is GroupBoundsData) {
        currentRect = boundsResult.bounds;
      } else if (boundsResult is Rect) {
        currentRect = boundsResult;
      } else {
        return; // Error getting bounds
      }
      final currentShapeCenterX = currentRect.center.dx;

      // Determine board center X (independent of zoom/pan for horizontal centering)
      final boardCenterX = Get.width / 2;

      // Calculate horizontal translation delta
      final deltaX = boardCenterX - currentShapeCenterX;

      // If already centered (or very close), do nothing
      if (deltaX.abs() < 0.5) {
        return;
      }

      final translationDelta = Offset(deltaX, 0);

      // Apply translation to the shape data
      ShapeData translatedShapeData;
      if (currentData is GroupShapeData) {
        final groupData = currentData;
        // Translate group vertices and center
        final newVertices =
            groupData.vertices.map((v) => v + translationDelta).toList();
        final newCenter = groupData.center + translationDelta;
        // Translate child shapes efficiently
        final updatedChildShapes = groupData.transformChildShapes(
          translation: translationDelta,
        );
        // Create new group data
        final newGroupData = groupData.copyWith(
          vertices: newVertices,
          center: newCenter,
          boundingRect: groupData.boundingRect.shift(translationDelta),
        ) as GroupShapeData;
        translatedShapeData = newGroupData.copyWithChildren(
          childShapes: updatedChildShapes,
        );
      } else {
        // Regular shape translation
        final newVertices =
            currentData.vertices.map((v) => v + translationDelta).toList();
        final newCenter = currentData.center + translationDelta;
        translatedShapeData = currentData.copyWith(
          vertices: newVertices,
          center: newCenter,
          boundingRect: currentData.boundingRect.shift(translationDelta),
        );
      }

      // Save state - don't track in history since this is part of shape creation
      saveShapeState(shapeKey, translatedShapeData);

      // Force rebuild the shape with the new center position
      int index = shapes.indexWhere((s) => s.key == shapeKey);
      if (index >= 0) {
        final shapeType = shapes[index].initialShapeType;
        final constraints = shapes[index].constraints;
        final newShape = TransformableShape(
          key: shapeKey,
          constraints: constraints,
          initialShapeType: shapeType,
          initialShapeData: translatedShapeData,
          selected: true,
        );
        shapes[index] = newShape;
      }
    } catch (e, stackTrace) {
      debugPrint("Error snapping new shape to center: $e\n$stackTrace");
    }
  }

  // Handle drag events for shapes
  void handleDragUpdate(
      BuildContext context, Offset globalPosition, DragUpdateDetails details) {
    // If no shapes are selected, do nothing
    if (selectedIndices.isEmpty) return;

    // Get screen size for calculations
    final size = MediaQuery.of(context).size;
    final screenWidth = size.width;
    final double baseScreenHeight = size.height;
    final double extendedGridHeight =
        GridConstants.getExtendedHeight(baseScreenHeight);

    // Raw delta from this update event
    final Offset rawDelta = details.delta;

    // Track if any snap happened (center or shape-to-shape) across all shapes
    bool didAnySnapOccurThisFrame = false;
    SnapInfo? winningSnapInfoForVisuals;

    // For multi-selection: move all selected shapes together
    for (int i = 0; i < selectedIndices.length; i++) {
      // Ensure index is valid
      if (selectedIndices[i] >= shapes.length) continue;

      // Get the shape widget and its key
      final shapeWidget = shapes[selectedIndices[i]];
      final shapeKey = shapeWidget.key;
      if (shapeKey == null) continue;

      // Get shape data state before moving
      final originalShapeData = shapeManager.shapeStates[shapeKey];
      if (originalShapeData == null) continue;

      // Extended constraints for grid system
      final BoxConstraints dragConstraints = BoxConstraints(
        minWidth: 0,
        maxWidth: screenWidth,
        minHeight: 0,
        maxHeight: extendedGridHeight,
      );

      // Mirror mode handling
      Offset effectiveDelta = rawDelta;
      if (isMirrorModeActive.value) {
        // In mirror mode, completely block horizontal movement
        effectiveDelta = Offset(0, rawDelta.dy);
      }

      // --- 1. Apply the drag delta to get potential new data ---

      // Updated logic: Try to predict where the shape would be after applying delta
      // by creating a temporary shape data (without saving it)
      ShapeData potentialShapeData = _calculatePotentialShapeData(
        originalShapeData: originalShapeData,
        delta: effectiveDelta,
        constraints: dragConstraints,
      );

      // --- 2. Determine if any snap should occur ---
      // Calculate snap *before* updating the shape to allow more precise snapping
      final snapCalculationResult = snappingManager.calculateDragSnap(
        originalShapeData: originalShapeData, // Pass data *before* delta
        potentialShapeData:
            potentialShapeData, // Pass data *after* delta + constraints
        globalPointerPosition: globalPosition,
        shapeKey: shapeKey,
        allShapeStates: shapeManager.shapeStates,
        constraints: dragConstraints,
        isCenterSnapEnabled: snapToCenter.value,
        // Professional snap parameters for smooth, predictable behavior
        shapeSnapThreshold: 12.0, // Reduced for less aggressive snapping
        centerSnapThreshold: 16.0, // Slightly higher for center line priority
        gridSnapThreshold: 8.0, // Reduced for lighter grid snapping
        isShapeSnapEnabled: true, // Always enable shape snapping
      );

      final Offset snapOffset = snapCalculationResult.snapOffset;
      final SnapInfo? currentSnapInfo = snapCalculationResult.snapInfo;
      final bool didCenterSnap = snapCalculationResult.didCenterSnap;

      // For debug/logging
      debugPrint("snapOffset: $snapOffset, didCenterSnap: $didCenterSnap");

      // 3. Apply the final movement using the shape manipulation handler
      final result = ShapeManipulationHandlers.handleShapeDrag(
        shapeData: originalShapeData,
        delta: effectiveDelta,
        constraints: dragConstraints,
        allShapeStates: shapeManager.shapeStates,
        currentShapeKey: shapeKey,
        // Pass snap information to handler
        getSnapInfo: (
            {required ShapeData originalShapeData,
            required ShapeData potentialShapeData,
            required Offset globalPointerPosition,
            required Key shapeKey}) {
          return snapCalculationResult;
        },
        globalPointerPosition: globalPosition,
      );

      final ShapeData finalShapeData = result.shapeData;

      // Update shape with final position
      shapeManager.shapeStates[shapeKey] = finalShapeData;

      // Update all shapes in mirror mode
      if (isMirrorModeActive.value) {
        // TODO: handle mirror mode constraints
      }

      // Track if any snap happened
      if (currentSnapInfo != null) {
        didAnySnapOccurThisFrame = true;
        winningSnapInfoForVisuals = currentSnapInfo;
      }

      // Update the shape UI
      _updateShapeUIAfterDrag(shapeKey, i);
    }

    // Check for auto-panning after all shapes have been updated
    _checkAutoPanForSelectedShapes(context);

    // Store winning snap info for visualization
    if (didAnySnapOccurThisFrame) {
      setActiveSnapInfo(winningSnapInfoForVisuals);
    } else {
      setActiveSnapInfo(null);
    }
  }

  /// Flip the selected shape horizontally across its center
  void flipSelectedShapeHorizontally() {
    if (selectedIndices.isEmpty) return;

    for (final index in selectedIndices) {
      if (index >= 0 && index < shapes.length) {
        final selectedShape = shapes[index];
        final Key? shapeKey = selectedShape.key;
        if (shapeKey == null) continue;

        final shapeData = shapeManager.shapeStates[shapeKey];
        if (shapeData == null) continue;

        final flippedData =
            _transformationManager.flipShapeHorizontally(shapeData);

        saveShapeState(
            shapeKey, flippedData); // This updates shapeManager.shapeStates

        final newShape = TransformableShape(
          key: shapeKey,
          constraints: selectedShape.constraints,
          initialShapeType: selectedShape.initialShapeType,
          initialRect: selectedShape.initialRect,
          selected: true,
          initialShapeData: flippedData,
          initialCurveMode: _uiStateManager.isCurveModeActive(shapeKey),
        );
        shapes[index] = newShape;
      }
    }

    // --- ADD HISTORY ENTRY HERE ---
    final operationName = "Flip Horizontally";
    debugPrint(
        "[ShapeEditorController] Adding history entry AFTER state update: $operationName");
    _addHistoryEntry(operationName);
    // -----------------------------

    update();
  }

  /// Flip the selected shape vertically across its center
  void flipSelectedShapeVertically() {
    if (selectedIndices.isEmpty) return;

    for (final index in selectedIndices) {
      if (index >= 0 && index < shapes.length) {
        final selectedShape = shapes[index];
        final Key? shapeKey = selectedShape.key;
        if (shapeKey == null) continue;

        final shapeData = shapeManager.shapeStates[shapeKey];
        if (shapeData == null) continue;

        final flippedData =
            _transformationManager.flipShapeVertically(shapeData);

        saveShapeState(shapeKey, flippedData); // Updates state

        final newShape = TransformableShape(
          key: shapeKey,
          constraints: selectedShape.constraints,
          initialShapeType: selectedShape.initialShapeType,
          initialRect: selectedShape.initialRect,
          selected: true,
          initialShapeData: flippedData,
          initialCurveMode: _uiStateManager.isCurveModeActive(shapeKey),
        );
        shapes[index] = newShape;
      }
    }

    // --- ADD HISTORY ENTRY HERE ---
    final operationName = "Flip Vertically";
    debugPrint(
        "[ShapeEditorController] Adding history entry AFTER state update: $operationName");
    _addHistoryEntry(operationName);
    // -----------------------------

    update();
  }

  /// Rotate the selected shapes by 45 degrees clockwise
  void rotateSelectedShapesByFixedAngle() {
    if (selectedIndices.isEmpty) return;

    // Define rotation angle
    const double rotationDegrees = 45.0;
    final rotationDelta = rotationDegrees * (math.pi / 180);

    // Start history tracking
    startHistoryTracking("Rotate $rotationDegrees° Clockwise");

    final List<int> currentSelection = List.from(selectedIndices);

    for (final index in currentSelection) {
      if (index < 0 || index >= shapes.length) continue;

      final selectedShapeWidget = shapes[index];
      final Key? shapeKey = selectedShapeWidget.key;
      if (shapeKey == null) continue;

      final originalShapeData = shapeManager.shapeStates[shapeKey];
      if (originalShapeData == null) continue;

      // Get the constraints from the widget
      final constraints = selectedShapeWidget.constraints;

      // Calculate the positions needed for handleRotation
      final center = originalShapeData.center;
      final radius = 1.0; // Arbitrary radius for angle calculation
      final lastPosition = Offset(center.dx + radius, center.dy); // 0 degrees
      final newPosition = Offset(
        center.dx + radius * math.cos(rotationDelta),
        center.dy + radius * math.sin(rotationDelta),
      ); // Rotated position

      // Use the ShapeManipulationHandlers.handleRotation method
      final rotatedData = ShapeManipulationHandlers.handleRotation(
        shapeData: originalShapeData,
        lastPosition: lastPosition,
        newPosition: newPosition,
        constraints: constraints, // Pass the constraints
      );

      // Check if the rotation was actually applied (i.e., didn't go out of bounds)
      if (rotatedData == originalShapeData) {
        // Rotation was prevented by constraints, maybe show a snackbar?
        Get.snackbar(
          'errors_rotationPrevented'.tr,
          '',
          snackPosition: SnackPosition.TOP,
          duration: const Duration(seconds: 1),
          backgroundColor: Colors.orange,
          colorText: Colors.white,
        );
        continue; // Skip to the next selected shape
      }

      // Save the new state (this updates shapeManager.shapeStates)
      saveShapeState(shapeKey, rotatedData);

      // --- IMPORTANT: Update the corresponding TransformableShape widget ---
      // Recreate the widget with the new data to reflect changes visually.
      // Ensure initialCurveMode reflects the state potentially modified by rotation handling.
      final newShapeWidget = TransformableShape(
        key: shapeKey,
        constraints: selectedShapeWidget.constraints,
        initialShapeType: selectedShapeWidget.initialShapeType,
        initialRect:
            selectedShapeWidget.initialRect, // Keep original rect if needed?
        selected: true, // Keep selection
        initialShapeData: rotatedData, // Pass the fully rotated data
        initialCurveMode: _uiStateManager.isCurveModeActive(shapeKey),
      );

      // Replace the old widget in the list
      // Ensure the index is still valid in case the list changed (though it shouldn't here)
      final currentWidgetIndex = shapes.indexWhere((s) => s.key == shapeKey);
      if (currentWidgetIndex != -1) {
        shapes[currentWidgetIndex] = newShapeWidget;
      } else {
        debugPrint(
            "Error: Could not find shape widget to update after rotation.");
      }
      // --- End Widget Update ---
    }

    // Finish history tracking
    finishHistoryTracking();

    // Trigger UI update
    update();
  }

  /// Toggle curve mode for the shape at the given index
  void toggleCurveMode(int index) {
    if (index < 0 || index >= shapes.length) return;

    final shape = shapes[index];
    final Key? shapeKey = shape.key;

    if (shapeKey == null) return;

    // Get the shape data to check if it's a group
    final shapeData = shapeManager.shapeStates[shapeKey];
    if (shapeData == null) return;

    // Don't allow toggling curve mode for group shapes
    if (shapeData.type == ShapeType.group || shapeData is GroupShapeData) {
      // If somehow curve mode is on for a group, turn it off
      if (_uiStateManager.isCurveModeActive(shapeKey)) {
        // Start history tracking for this operation
        startHistoryTracking("Disable Curve Mode");

        // Set curve mode off
        _uiStateManager.toggleCurveMode(shapeKey, false);

        // Toggle curve mode on the shape widget to turn it off
        shape.toggleCurveMode();

        // Finish history tracking
        finishHistoryTracking();
      }
      return;
    }

    // Start history tracking for this operation
    startHistoryTracking("Toggle Curve Mode");

    // Toggle the curve mode state in our map
    bool currentMode = _uiStateManager.isCurveModeActive(shapeKey);
    _uiStateManager.toggleCurveMode(shapeKey, !currentMode);

    // Toggle curve mode on the shape widget
    shape.toggleCurveMode();

    // Finish history tracking
    finishHistoryTracking();
  }

  // Method to deselect all shapes
  void deselectAllShapes() {
    selectionManager.deselectAllShapes();
    update();
  }

  // Method to toggle shape menu open/closed state
  void toggleShapeMenu() {
    _uiStateManager.toggleShapeMenu();
    update();
  }

  // Method to toggle toolbar expansion state
  void toggleToolbarExpanded() {
    _uiStateManager.toggleToolbarExpanded();
    update();
  }

  /// Group the currently selected shapes
  void groupSelectedShapes() {
    if (!_groupManager.canGroupSelectedShapes()) return;

    // Perform the grouping (this modifies shapeManager state internally)
    if (_groupManager.groupSelectedShapes()) {
      // --- ADD HISTORY ENTRY HERE ---
      final operationName = "Group Shapes";
      debugPrint(
          "[ShapeEditorController] Adding history entry AFTER state update: $operationName");
      _addHistoryEntry(operationName);
      // -----------------------------
      update();
    }
  }

  /// Ungroup the currently selected group shape
  void ungroupSelectedShape() {
    if (!_groupManager.canUngroupSelectedShape()) return;

    // Perform the ungrouping (this modifies shapeManager state internally)
    if (_groupManager.ungroupSelectedShape()) {
      // --- ADD HISTORY ENTRY HERE ---
      final operationName = "Ungroup Shape";
      debugPrint(
          "[ShapeEditorController] Adding history entry AFTER state update: $operationName");
      _addHistoryEntry(operationName);
      // -----------------------------
      update();
    }
  }

  /// Check if the currently selected shapes can be grouped
  bool canGroupSelectedShapes() {
    return _groupManager.canGroupSelectedShapes();
  }

  /// Check if the currently selected shape can be ungrouped
  bool canUngroupSelectedShape() {
    return _groupManager.canUngroupSelectedShape();
  }

  /// Saves the state for all shapes
  void saveAllShapesState() {
    // Make sure all shapes have been converted to grid coordinates
    if (!hasConvertedToGridCoordinates.value) {
      _convertShapesToGridCoordinates();
    }

    // Store all shape states
    shapeManager.saveAllShapesState();
  }

  /// Save the current shapes to the wizard state
  /// This ensures all shapes are saved to both Firestore and local storage
  Future<void> saveToWizardState() async {
    try {
      // First ensure all shapes are converted to grid coordinates for device independence
      if (!hasConvertedToGridCoordinates.value) {
        _convertShapesToGridCoordinates();
      }

      // Only proceed if we're in the wizard
      if (!Get.isRegistered<NewItemWizardController>()) {
        return;
      }

      final wizardController = Get.find<NewItemWizardController>();

      // Check if we have a state ID
      if (wizardController.currentStateId.value == null) {
        return;
      }

      // Load the current wizard state
      final state = await wizardController.wizardStateService
          .loadWizardState(wizardController.currentStateId.value!);

      if (state == null) {
        return;
      }

      // Extract the shape data from all shapes
      final shapeDataList = <ShapeData>[];
      for (final key in shapeManager.shapeKeysOrder) {
        final shapeData = shapeManager.shapeStates[key];
        if (shapeData != null) {
          // Add to the list, keeping the grid coordinates
          shapeDataList.add(shapeData);
        }
      }

      // Create a new ShapeTestState with the current shapes
      final shapeTestState = ShapeTestState(shapes: shapeDataList);

      // Create an updated wizard state with the new shape test state
      final updatedState = state.copyWith(
        shapeTestState: shapeTestState,
        lastModified: DateTime.now(),
      );

      // Save the updated wizard state
      await wizardController.wizardStateService.saveWizardState(updatedState);

      debugPrint('Saved ${shapeDataList.length} shapes to wizard state');
    } catch (e, stackTrace) {
      debugPrint('Error saving shapes to wizard state: $e');
      debugPrint('Stack trace: $stackTrace');
    }
  }

  /// Toggle mirror mode on/off
  void toggleMirrorMode() {
    // If entering mirror mode and multi-selection is enabled, disable it first
    if (!isMirrorModeActive.value && isMultiSelectionMode) {
      isMultiSelectionMode = false;
    }

    _mirrorModeManager.toggleMirrorMode();

    // Ensure UI is completely updated
    WidgetsBinding.instance.addPostFrameCallback((_) {
      update();
    });
  }

  /// Check if a shape is mirrored
  bool isShapeMirrored(Key shapeKey) {
    return _mirrorModeManager.isShapeMirrored(shapeKey);
  }

  /// Get the mirrored pair for a shape
  Key? getMirroredPair(Key shapeKey) {
    return _mirrorModeManager.getMirroredPair(shapeKey);
  }

  /// Handle shape transformation in mirror mode
  /// Delegates to the mirror mode manager
  void handleShapeTransformation(Key shapeKey) {
    _mirrorModeManager.handleShapeTransformation(shapeKey);
  }

  /// Constrain a shape position during drag to respect mirror mode constraints
  /// Returns the constrained shape data if changes were needed, otherwise returns null
  ShapeData? constrainShapePosition(Key shapeKey, ShapeData currentData) {
    return _mirrorModeManager.constrainPositionDuringDrag(
        shapeKey, currentData);
  }

  /// Handle tap on a shape with the given key
  void handleShapeTap(Key shapeKey) {
    // Find the shape with this key
    TransformableShape? tappedShape;
    for (final shape in shapes) {
      if (shape.key == shapeKey) {
        tappedShape = shape;
        break;
      }
    }

    if (tappedShape != null) {
      // Use the existing selectShape method
      selectionManager.selectShape(tappedShape);
    }
  }

  /// Duplicates the currently selected shape(s) and places the duplicates nearby
  void duplicateSelectedShapes() {
    if (selectedIndices.isEmpty) return;

    final List<int> newIndices = [];
    final List<TransformableShape> addedShapes =
        []; // Keep track of shapes to add *after* loop

    for (final index in List.from(selectedIndices)) {
      // Iterate over a copy
      if (index < 0 || index >= shapes.length) continue;

      final sourceShape = shapes[index];
      final sourceKey = sourceShape.key;
      if (sourceKey == null) continue;

      // Crucially, get the state *before* potential modification by createOffsetShapeData if it were stateful
      final sourceData = shapeManager.shapeStates[sourceKey]?.deepCopy();
      if (sourceData == null) continue;

      final duplicateKey = GlobalKey();

      // Create offset data
      final offsetData = shapeManager.createOffsetShapeData(
          sourceData, 30, 30); // Modifies copy

      // Store the NEW shape state
      shapeManager.saveShapeState(duplicateKey, offsetData);
      // Add key to order *after* storing state
      shapeManager.shapeKeysOrder.add(duplicateKey);
      // Initialize curve mode state
      _uiStateManager.curveModeStates[duplicateKey] = false;

      final duplicateShape = TransformableShape(
        key: duplicateKey,
        constraints: sourceShape.constraints,
        initialShapeType: sourceShape.initialShapeType, // Use original type
        initialShapeData: offsetData, // Use the offset data
      );

      addedShapes.add(duplicateShape); // Add to temp list
    }

    // Add all duplicated shapes to the main list
    shapes.addAll(addedShapes);

    // Update selection to the new shapes AFTER adding them
    selectedIndices.clear();
    for (final newShape in addedShapes) {
      int newIndex = shapes.indexWhere((s) => s.key == newShape.key);
      if (newIndex != -1) {
        newIndices.add(newIndex);
      }
    }
    selectedIndices.addAll(newIndices);

    // --- ADD HISTORY ENTRY HERE ---
    final operationName = "Duplicate Shape${addedShapes.length > 1 ? 's' : ''}";
    debugPrint(
        "[ShapeEditorController] Adding history entry AFTER state update: $operationName");
    _addHistoryEntry(operationName);
    // -----------------------------

    update();
  }

  /// Make a snapshot of the current state for history
  void _addHistoryEntry([String? operationName]) {
    debugPrint(
        "[ShapeEditorController] _addHistoryEntry called for: ${operationName ?? 'Unnamed operation'}");
    _undoRedoManager.addHistoryEntry(
      shapeManager.shapeStates,
      shapeManager.shapeKeysOrder,
      selectedIndices.toList(),
      _uiStateManager.curveModeStates,
      operationName,
    );
    isUndoRedoAnimating.value = false;
  }

  /// Undo the last action with visual feedback
  void undo() {
    final operationName = _undoRedoManager.getLastUndoOperationName();
    debugPrint(
        "[ShapeEditorController] Attempting Undo: ${operationName ?? 'Initial State'}");
    _showUndoRedoFeedback('Undo', operationName);

    final previousState = _undoRedoManager.undo(
      shapeManager.shapeStates,
      shapeManager.shapeKeysOrder,
      selectedIndices.toList(),
      _uiStateManager.curveModeStates,
    );

    if (previousState != null) {
      debugPrint("[ShapeEditorController] Applying previous state from Undo.");
      _applyHistoryState(previousState);
    } else {
      // Handle case where undo reaches the beginning (null state)
      if (_initialLoadedStateEntry != null) {
        debugPrint(
            "[ShapeEditorController] Undo reached beginning of history. Restoring LOADED initial state.");
        _applyHistoryState(_initialLoadedStateEntry!);
      } else {
        debugPrint(
            "[ShapeEditorController] Undo reached beginning of history. Resetting to EMPTY initial state.");
        _resetToInitialEmptyState();
      }
    }
  }

  /// Redo the previously undone action with visual feedback
  void redo() {
    final operationName = _undoRedoManager.getNextRedoOperationName();
    debugPrint(
        "[ShapeEditorController] Attempting Redo: ${operationName ?? 'No action'}");
    _showUndoRedoFeedback('Redo', operationName);
    final nextState = _undoRedoManager.redo();
    if (nextState != null) {
      debugPrint("[ShapeEditorController] Applying next state from Redo.");
      _applyHistoryState(nextState);
    } else {
      debugPrint("[ShapeEditorController] Redo found no state to apply.");
    }
  }

  /// Show visual feedback for undo/redo operations
  void _showUndoRedoFeedback(String operation, String? operationName) {
    isUndoRedoAnimating.value = true;
    animatingOperation.value = operationName ?? operation;
    Future.delayed(const Duration(milliseconds: 800), () {
      isUndoRedoAnimating.value = false;
    });
  }

  /// Apply a state from history (includes curve modes)
  void _applyHistoryState(HistoryEntry historyEntry) {
    debugPrint(
        "[ShapeEditorController] Applying history state. Operation: ${historyEntry.operationName ?? 'Unknown'}");
    debugPrint("  Shape Count: ${historyEntry.shapeStates.length}");
    debugPrint("  Selected Indices: ${historyEntry.selectedIndices}");
    debugPrint(
        "  Curve Mode Keys: ${historyEntry.curveModeStates.keys.length}");

    shapeManager.shapeStates.clear();
    for (final entry in historyEntry.shapeStates.entries) {
      shapeManager.shapeStates[entry.key] = entry.value;
    }
    shapeManager.shapeKeysOrder.clear();
    shapeManager.shapeKeysOrder.addAll(historyEntry.shapeOrder);

    // Restore curve mode states
    _uiStateManager.curveModeStates.clear();
    _uiStateManager.curveModeStates.addAll(historyEntry.curveModeStates);

    // Important: Clear selection before rebuilding shapes
    selectedIndices.clear();

    // Rebuild all shapes with the new data
    _rebuildShapesFromHistory();

    // Apply selection *after* shapes are rebuilt
    selectedIndices.addAll(historyEntry.selectedIndices);

    // Ensure UI is refreshed
    update();
    debugPrint("[ShapeEditorController] Finished applying history state.");
  }

  /// Reset the editor to a clean, empty state.
  void _resetToInitialEmptyState() {
    debugPrint("[ShapeEditorController] Resetting to initial empty state.");
    shapeManager.shapes.clear();
    shapeManager.shapeStates.clear();
    shapeManager.shapeKeysOrder.clear();
    _uiStateManager.curveModeStates.clear();
    selectedIndices.clear();
    _initialLoadedStateEntry = null; // Ensure loaded state is forgotten
    // Optionally reset zoom/pan here if desired
    // resetZoomAndCenterContent();
    update(); // Ensure UI reflects the empty state
  }

  void _rebuildShapesFromHistory() {
    debugPrint("[ShapeEditorController] Rebuilding shapes from history...");
    final List<TransformableShape> newShapes = [];
    for (final key in shapeManager.shapeKeysOrder) {
      if (shapeManager.shapeStates.containsKey(key)) {
        final shapeData = shapeManager.shapeStates[key]!;
        final screenShapeData = hasConvertedToGridCoordinates.value
            ? shapeData.fromGridCoordinates(gridCoordinateConverter)
            : shapeData;

        // BUGFIX: This is the problem - selectedIndices hasn't been updated yet when rebuilding
        // We need to check against the original keys that will be selected, not the current indices
        // Find this shape's position in the shapeKeysOrder list for proper selection index
        final shapeIndex = shapeManager.shapeKeysOrder.indexOf(key);
        final isSelected = selectedIndices.contains(shapeIndex);

        final extendedConstraints = BoxConstraints(
          maxWidth: Get.width,
          maxHeight: GridConstants.getExtendedHeight(Get.height),
        );

        final shape = TransformableShape(
          key: key,
          constraints: extendedConstraints,
          initialShapeType: screenShapeData.type,
          selected: isSelected,
          initialShapeData: screenShapeData,
          initialCurveMode: _uiStateManager.isCurveModeActive(key),
        );

        newShapes.add(shape);
      }
    }
    shapes.clear();
    shapes.addAll(newShapes);
  }

  /// Start tracking history for a shape manipulation (includes curve modes)
  void startHistoryTracking([String? operationName]) {
    _undoRedoManager.startHistoryTracking(
      shapeManager.shapeStates,
      shapeManager.shapeKeysOrder,
      selectedIndices.toList(),
      _uiStateManager.curveModeStates,
      operationName,
    );
  }

  /// Finish tracking history and record if changes occurred (includes curve modes)
  void finishHistoryTracking() {
    _undoRedoManager.finishHistoryTracking(
      shapeManager.shapeStates,
      shapeManager.shapeKeysOrder,
      selectedIndices.toList(),
      _uiStateManager.curveModeStates,
    );
    saveToWizardState();
  }

  /// Override dispose to clean up resources
  /// Check if auto-panning is needed for the currently selected shapes
  void _checkAutoPanForSelectedShapes(BuildContext context) {
    if (selectedIndices.isEmpty) {
      _autoPanManager.stopAutoPan();
      return;
    }

    // Get viewport size
    final Size viewportSize = MediaQuery.of(context).size;

    // Calculate combined bounds of all selected shapes
    Rect? combinedBounds;

    for (int i = 0; i < selectedIndices.length; i++) {
      if (selectedIndices[i] >= shapes.length) continue;

      final shapeWidget = shapes[selectedIndices[i]];
      final shapeKey = shapeWidget.key;
      if (shapeKey == null) continue;

      final shapeData = shapeManager.shapeStates[shapeKey];
      if (shapeData == null) continue;

      final shapeBounds = shapeData.boundingRect;

      if (combinedBounds == null) {
        combinedBounds = shapeBounds;
      } else {
        combinedBounds = combinedBounds.expandToInclude(shapeBounds);
      }
    }

    if (combinedBounds == null) {
      _autoPanManager.stopAutoPan();
      return;
    }

    // Check if auto-panning is needed and start/update it
    _autoPanManager.checkAndStartAutoPan(
      shapeGlobalBounds: combinedBounds,
      viewportSize: viewportSize,
      currentPanOffset: panOffset.value,
      currentZoom: zoomScale.value,
    );
  }

  @override
  void dispose() {
    debugPrint("[ShapeEditorController] Disposing...");
    selectionManager.deselectAllShapes();
    saveToWizardState();
    _autoSaveTimer?.cancel();
    _undoRedoManager.dispose();
    _autoPanManager.dispose(); // Dispose auto-pan manager
    transformationController.dispose(); // Add this line
    super.dispose();
    debugPrint("[ShapeEditorController] Disposed.");
  }

  void resetShapes() {
    final operationName = "Reset Shapes";
    debugPrint(
        "[ShapeEditorController] Starting history tracking for: $operationName");
    // History tracking for reset is tricky. Does reset create a new undo point?
    // Option 1: Don't track reset, just clear history.
    // Option 2: Track it, so users can undo the reset.
    // Current implementation tracks it. Let's keep it but ensure initial state is cleared.
    // startHistoryTracking(operationName); // Keep tracking for now
    shapeManager.resetShapes();
    _uiStateManager.curveModeStates.clear();
    selectedIndices.clear();
    _initialLoadedStateEntry = null; // Reset must forget loaded state
    debugPrint(
        "[ShapeEditorController] Shapes reset. Adding history entry for reset.");
    // If tracking, add the entry *after* resetting state
    _addHistoryEntry(operationName);
    // Alternatively, clear history completely after reset:
    // _undoRedoManager.clearHistory();
    update();
  }

  /// Update grid properties, including aspect ratio
  void updateGridProperties({
    double? newAspectRatio,
    double? newStitchesPerCm,
    double? newRowsPerCm,
    bool? showNeedleLabels,
  }) {
    if (newAspectRatio != null) {
      aspectRatio.value = newAspectRatio;
    }
    if (newStitchesPerCm != null) {
      stitchesPerCm.value = newStitchesPerCm;
    }
    if (newRowsPerCm != null) {
      rowsPerCm.value = newRowsPerCm;
    }

    // Recalculate aspect ratio if gauge changed
    double calculatedAspectRatio = aspectRatio.value;
    if ((newStitchesPerCm != null || newRowsPerCm != null) &&
        rowsPerCm.value > 0 &&
        stitchesPerCm.value > 0) {
      calculatedAspectRatio = stitchesPerCm.value / rowsPerCm.value;
      aspectRatio.value =
          calculatedAspectRatio; // Update the controller's state
    }

    gridSystem = GridSystem(
      cellWidth: gridSystem.cellWidth,
      primaryColor: gridSystem.primaryColor,
      gridColor: gridSystem.gridColor,
      opacity: gridSystem.opacity,
      zoomLevel: gridSystem.zoomLevel,
      panOffset: gridSystem.panOffset,
      needleCount: gridSystem.needleCount,
      snapToGrid: gridSystem.snapToGrid,
      snapThreshold: gridSystem.snapThreshold,
      machinePitch: gridSystem.machinePitch,
      snapToCenter: gridSystem.snapToCenter,
      showNeedleLabels: showNeedleLabels ?? gridSystem.showNeedleLabels,
      aspectRatio: calculatedAspectRatio, // Use calculated value
    );

    update(); // Force UI update
  }

  // Add a snap indicator at the specified position
  void addSnapIndicator(Offset position, {bool isCenterSnap = false}) {
    final indicator = SnapIndicator(
      key: GlobalKey(),
      position: position,
      isCenterSnap: isCenterSnap, // Pass the flag to the indicator
    );
    snapIndicators.add(indicator);

    // Increase duration for better visibility - especially for center snaps
    Future.delayed(Duration(milliseconds: isCenterSnap ? 1000 : 700), () {
      if (snapIndicators.contains(indicator)) {
        snapIndicators.remove(indicator);
      }
    });

    // Add haptic feedback to make snaps more tactile
    HapticFeedback.mediumImpact();
  }

  // Helper method to collapse toolbar rows
  void _collapseToolbarRows() {
    if (toolbarKey.currentState != null) {
      toolbarKey.currentState?.collapseAllRows();
    }
  }

  /// Called by EnhancedMirrorModeManager after mirror mode is deactivated
  void groupMirroredPairs(Map<Key, Key> pairsToGroup) {
    debugPrint(
        "[ShapeEditorController] Attempting to group ${pairsToGroup.length} mirrored pairs.");
    bool didGroupAny = false;

    for (final entry in pairsToGroup.entries) {
      final originalKey = entry.key;
      final mirrorKey = entry.value;

      // Check if both shapes still exist (might have been deleted)
      if (shapeManager.shapeStates.containsKey(originalKey) &&
          shapeManager.shapeStates.containsKey(mirrorKey)) {
        debugPrint("  - Grouping pair: $originalKey and $mirrorKey");
        // Use the GroupManager to perform the grouping based on keys
        // Pass the current `shapes` list for widget manipulation
        final success =
            _groupManager.groupShapesByKey([originalKey, mirrorKey], shapes);
        if (success) {
          didGroupAny = true;
        } else {
          debugPrint("  - Failed to group pair: $originalKey and $mirrorKey");
        }
      } else {
        debugPrint(
            "  - Skipping pair (one or both shapes missing): $originalKey and $mirrorKey");
      }
    }

    // If any grouping actually happened, add a history entry and update UI
    if (didGroupAny) {
      final operationName = "Group Mirrored Shapes";
      debugPrint(
          "[ShapeEditorController] Adding history entry AFTER grouping: $operationName");
      _addHistoryEntry(operationName);
      selectedIndices.clear(); // Clear selection after grouping
      update(); // Update UI to show the new group(s)
    } else {
      debugPrint("[ShapeEditorController] No pairs were successfully grouped.");
    }
  }

  /// Reset the shape editor by clearing all shapes and states
  void clearAllShapes() {
    debugPrint("[ShapeEditorController] Clearing all shapes and states");

    // Clear all shapes and states
    shapeManager.shapes.clear();
    shapeManager.shapeStates.clear();
    shapeManager.shapeKeysOrder.clear();
    _uiStateManager.curveModeStates.clear();
    selectedIndices.clear();

    // Clear mirror mode pairs if active
    if (isMirrorModeActive.value) {
      _mirrorModeManager.clearMirrorState();
    }

    // Reset history
    _undoRedoManager.clearHistory();
    _initialLoadedStateEntry = null;

    // Force UI update
    update();
  }

  // --- Added for Snap Lines Visual Feedback ---
  /// Updates the currently active snap information.
  void setActiveSnapInfo(SnapInfo? info) {
    // Check if the info actually changed to avoid unnecessary updates
    if (activeSnapInfo.value?.targetShapeKey != info?.targetShapeKey ||
        activeSnapInfo.value?.snapType != info?.snapType) {
      // When snap occurs, provide haptic feedback for tactile confirmation
      if (info != null && activeSnapInfo.value == null) {
        HapticFeedback.lightImpact(); // Light impact for ongoing snaps
      }

      activeSnapInfo.value = info;
      // No need to call update() here as activeSnapInfo is Rx
    }
  }
  // --- End Added ---

  /// Toggle the persistent multi-selection mode.
  void toggleMultiSelectionMode() {
    // Prevent entering multi-selection mode if there's only one shape or no shapes
    if (!isMultiSelectionMode && shapes.length <= 1) {
      Get.snackbar(
        'Multi-selection',
        'At least two shapes are required for multi-selection mode.',
        duration: const Duration(seconds: 2),
        snackPosition: SnackPosition.BOTTOM,
      );
      return;
    }

    // Use the setter which includes the mirror mode check
    isMultiSelectionMode = !isMultiSelectionMode;
    // If turning multi-selection off, also ensure isMultiSelecting flag is false
    if (!isMultiSelectionMode) {
      selectionManager.isMultiSelecting.value = false;
    }
    update(); // Ensure UI updates if needed
  }

  // Clipboard for storing copied/cut shapes
  final Rx<List<ShapeData>> _clipboard = Rx<List<ShapeData>>([]);

  // Position where the clipboard content will be pasted if no specific position is provided
  final Rx<Offset?> _pastePosition = Rx<Offset?>(null);

  // --- Context Menu Methods ---

  // Show context menu - Stores initial position and triggers calculation (via post-frame callback)
  void showContextMenu(Offset globalPosition) {
    initialContextMenuPosition.value = globalPosition;
    calculatedContextMenuPosition.value =
        globalPosition; // Start calculation from initial pos
    isContextMenuVisible.value = true;
    // Calculation will happen in ShapeContextMenu's post-frame callback
    update();
  }

  // Hide context menu
  void hideContextMenu() {
    if (isContextMenuVisible.value) {
      isContextMenuVisible.value = false;
      initialContextMenuPosition.value = null;
      calculatedContextMenuPosition.value = null; // Clear calculated position
      update();
    }
  }

  // Calculate and update the menu position to stay within bounds
  // Called from ShapeContextMenu's post-frame callback
  void calculateAndSetMenuPosition(BuildContext context) {
    if (!isContextMenuVisible.value ||
        initialContextMenuPosition.value == null ||
        contextMenuKey.currentContext == null) {
      return; // Don't calculate if not visible or key context not ready
    }

    final RenderBox? menuRenderBox =
        contextMenuKey.currentContext!.findRenderObject() as RenderBox?;
    if (menuRenderBox == null) return; // Cannot get size yet

    final Size menuSize = menuRenderBox.size;
    final Size screenSize = MediaQuery.of(context).size;
    final Offset initialPosition = initialContextMenuPosition.value!;

    double finalLeft = initialPosition.dx;
    double finalTop = initialPosition.dy;
    const double padding = 8.0; // Padding from screen edges

    // Adjust horizontally
    if (finalLeft + menuSize.width > screenSize.width - padding) {
      finalLeft = screenSize.width - menuSize.width - padding;
    }
    if (finalLeft < padding) {
      finalLeft = padding;
    }

    // Adjust vertically
    if (finalTop + menuSize.height > screenSize.height - padding) {
      // Try placing it above the initial point first
      double topAbove = initialPosition.dy - menuSize.height - padding;
      if (topAbove >= padding) {
        // Check if placing above fits
        finalTop = topAbove;
      } else {
        // If placing above also doesn't fit, clamp to bottom
        finalTop = screenSize.height - menuSize.height - padding;
      }
    }
    if (finalTop < padding) {
      // Clamp to top if needed (e.g., after trying to place above)
      finalTop = padding;
    }

    // TODO: Add logic here to check for HUD/Toolbar overlap if needed

    final Offset newPosition = Offset(finalLeft, finalTop);

    // Only update if the calculated position actually changed
    if (calculatedContextMenuPosition.value != newPosition) {
      calculatedContextMenuPosition.value = newPosition;
      // Don't call update() here to avoid potential build loops,
      // the Obx in the view will react to calculatedContextMenuPosition change.
      WidgetsBinding.instance.addPostFrameCallback((_) {
        // Need to ensure the state update happens safely after build
        if (isContextMenuVisible.value) {
          // Check again if still visible
          update(); // Trigger GetBuilder update if necessary
        }
      });
    }
  }

  // Helper method to check if a tap is inside the calculated context menu bounds
  bool isTapInsideContextMenu(Offset globalTapPosition) {
    if (!isContextMenuVisible.value ||
        calculatedContextMenuPosition.value == null || // Use calculated pos
        contextMenuKey.currentContext == null) {
      return false;
    }
    final RenderBox? renderBox =
        contextMenuKey.currentContext!.findRenderObject() as RenderBox?;
    if (renderBox == null) return false;

    // Use calculated position for the rect check
    final menuPosition = calculatedContextMenuPosition.value!;
    final menuSize = renderBox.size;
    final menuRect = Rect.fromLTWH(
        menuPosition.dx, menuPosition.dy, menuSize.width, menuSize.height);

    final isInside = menuRect.contains(globalTapPosition);
    return isInside;
  }

  // Method to check if the selected shape is a group
  bool isGroupSelected() {
    if (selectedIndices.isEmpty) return false;

    // Check if any of the selected shapes is a group
    for (final index in selectedIndices) {
      if (index < 0 || index >= shapes.length) continue;

      final shape = shapes[index];
      final shapeKey = shape.key;
      if (shapeKey == null) continue;

      final shapeData = shapeStates[shapeKey];
      if (shapeData == null) continue;

      if (shapeData.type == ShapeType.group || shapeData is GroupShapeData) {
        return true;
      }
    }

    return false;
  }

  // Method to copy selected shapes to clipboard
  void copySelectedShapes() {
    if (selectedIndices.isEmpty) return;

    _clipboard.value = [];
    List<ShapeData> shapesToCopy = [];

    for (final index in selectedIndices) {
      if (index < 0 || index >= shapes.length) continue;

      final shape = shapes[index];
      final shapeKey = shape.key;
      if (shapeKey == null) continue;

      final shapeData = getInternalShapeState(shapeKey)?.deepCopy();
      if (shapeData == null) continue;

      shapesToCopy.add(shapeData);
    }

    _clipboard.value = shapesToCopy;

    // Store the first selected shape's position as a reference for pasting
    if (selectedIndices.isNotEmpty &&
        selectedIndices[0] >= 0 &&
        selectedIndices[0] < shapes.length) {
      final refShape = shapes[selectedIndices[0]];
      final refKey = refShape.key;
      if (refKey != null) {
        final refData = shapeStates[refKey];
        if (refData != null) {
          _pastePosition.value = refData.center;
        }
      }
    }
  }

  // Method to cut selected shapes (copy + delete)
  void cutSelectedShapes() {
    copySelectedShapes();
    deleteSelectedShapes();
  }

  // Method to paste shapes from clipboard at a specific position
  void pasteShapes([Offset? position]) {
    if (_clipboard.value.isEmpty) return;

    final pastePos = position ??
        _pastePosition.value ??
        Offset(Get.width / 2, Get.height / 2);

    // Calculate the center of copied shapes to determine the offset
    Offset clipboardCenter = Offset.zero;
    int count = 0;

    for (final shapeData in _clipboard.value) {
      clipboardCenter += shapeData.center;
      count++;
    }

    if (count > 0) {
      clipboardCenter = clipboardCenter / count.toDouble();
    }

    // Calculate offset from clipboard center to paste position
    final offset = pastePos - clipboardCenter;

    // Start tracking for history
    startHistoryTracking(
        "Paste Shape${_clipboard.value.length > 1 ? 's' : ''}");

    // Clear selection before pasting
    selectedIndices.clear();

    // Add all shapes with offset applied
    final newShapeIndices = <int>[];

    for (final originalData in _clipboard.value) {
      // Create a copy of the shape with translated coordinates
      final translatedVertices =
          originalData.vertices.map((v) => v + offset).toList();
      final translatedCenter = originalData.center + offset;
      final translatedBoundingRect = originalData.boundingRect.shift(offset);

      ShapeData translatedData;

      if (originalData is GroupShapeData) {
        // Handle group shapes with their children
        final translatedChildren = originalData.childShapes.map((childShape) {
          final childVertices =
              childShape.vertices.map((v) => v + offset).toList();
          final childCenter = childShape.center + offset;
          final childBoundingRect = childShape.boundingRect.shift(offset);

          return childShape.copyWith(
            vertices: childVertices,
            center: childCenter,
            boundingRect: childBoundingRect,
          );
        }).toList();

        translatedData = (originalData).copyWithChildren(
          childShapes: translatedChildren,
          vertices: translatedVertices,
          center: translatedCenter,
          boundingRect: translatedBoundingRect,
        );
      } else {
        translatedData = originalData.copyWith(
          vertices: translatedVertices,
          center: translatedCenter,
          boundingRect: translatedBoundingRect,
        );
      }

      // Create a new shape
      final shapeKey = GlobalKey();
      final constraints = BoxConstraints(
        maxWidth: Get.width,
        maxHeight: GridConstants.getExtendedHeight(Get.height),
      );

      // Initialize curve mode state for the new shape
      _uiStateManager.curveModeStates[shapeKey] = false;

      // Save the shape state first
      if (hasConvertedToGridCoordinates.value) {
        shapeStates[shapeKey] =
            translatedData.toGridCoordinates(gridCoordinateConverter);
      } else {
        shapeStates[shapeKey] = translatedData;
      }

      // Add to the keys order
      shapeKeysOrder.add(shapeKey);

      // Create the visual shape
      final newShape = TransformableShape(
        key: shapeKey,
        constraints: constraints,
        initialShapeType: translatedData.type,
        initialShapeData: translatedData,
        selected: true,
      );

      // Add to shapes list
      shapes.add(newShape);

      // Track the index for selection
      newShapeIndices.add(shapes.length - 1);
    }

    // Select the newly pasted shapes
    selectedIndices.addAll(newShapeIndices);

    // Finish history tracking
    finishHistoryTracking();

    // Update UI
    update();
  }

  // Check if there's content in the clipboard
  bool canPaste() {
    return _clipboard.value.isNotEmpty;
  }

  /// Handle long press events directly on the canvas.
  /// Determines selection changes before the context menu is shown.
  void handleCanvasLongPress(BuildContext context, Offset tapGridPosition) {
    // CRITICAL: Do not process if menu is already visible or handle interaction
    if (isContextMenuVisible.value || isHandleInteraction) {
      return;
    }

    bool hitShape = false;
    int hitShapeIndex = -1;

    // Perform hit testing (similar to handleTapEvent but simplified for long press)
    List<TransformableShape> shapeList = List.from(shapes);
    for (int i = shapeList.length - 1; i >= 0; i--) {
      final shape = shapeList[i];
      final Key? shapeKey = shape.key;
      if (shapeKey == null) continue;
      final shapeData = shapeManager.shapeStates[shapeKey];
      if (shapeData == null) continue;

      // Reuse hit test path creation logic
      final path = selectionManager.createHitTestPath(shapeData);
      final Matrix4 shapeTransform = Matrix4.identity()
        ..translate(shapeData.center.dx, shapeData.center.dy)
        ..rotateZ(shapeData.rotation)
        ..translate(-shapeData.center.dx, -shapeData.center.dy);
      final Path transformedPath = path.transform(shapeTransform.storage);

      if (transformedPath.contains(tapGridPosition)) {
        hitShape = true;
        hitShapeIndex = i;
        break; // Found the topmost shape hit
      }
    }

    if (hitShape) {
      // Long pressed on a shape
      if (isMultiSelectionMode) {
        // In multi-selection mode:
        // If the shape is not already selected, add it to the selection
        if (!selectedIndices.contains(hitShapeIndex)) {
          selectionManager.toggleShapeSelection(hitShapeIndex, true);
        }
        // If already selected, do nothing (preserve selection)
      } else {
        // Not in multi-selection mode: select only this shape
        // but only if it's not already selected
        if (!selectedIndices.contains(hitShapeIndex)) {
          selectionManager.selectShape(shapeList[hitShapeIndex]);
        }
      }
    }
    // Don't deselect anything on background long press
    // (only on regular taps, which is handled in handleTapEvent)

    // No need to update() here, as showContextMenu will trigger an update
  }

  // Helper method to calculate potential shape data after applying a delta
  ShapeData _calculatePotentialShapeData({
    required ShapeData originalShapeData,
    required Offset delta,
    required BoxConstraints constraints,
  }) {
    // Apply vertices translation
    final potentialVertices = originalShapeData.vertices
        .map((vertex) => vertex.translate(delta.dx, delta.dy))
        .toList();
    final potentialCenter =
        originalShapeData.center.translate(delta.dx, delta.dy);

    // Create a temporary shape data
    final tempShapeData = originalShapeData.copyWith(
      vertices: potentialVertices,
      center: potentialCenter,
    );

    // Calculate accurate bounds
    final accurateBoundsResult =
        GeometryUtils.calculateAccurateBoundingRect(tempShapeData);
    Rect potentialBounds;
    if (accurateBoundsResult is GroupBoundsData) {
      potentialBounds = accurateBoundsResult.bounds;
    } else {
      potentialBounds = accurateBoundsResult as Rect;
    }

    // Apply boundary constraints
    double dxCorrection = 0.0;
    double dyCorrection = 0.0;

    if (potentialBounds.left < constraints.minWidth) {
      dxCorrection = constraints.minWidth - potentialBounds.left;
    } else if (potentialBounds.right > constraints.maxWidth) {
      dxCorrection = constraints.maxWidth - potentialBounds.right;
    }

    if (potentialBounds.top < constraints.minHeight) {
      dyCorrection = constraints.minHeight - potentialBounds.top;
    } else if (potentialBounds.bottom > constraints.maxHeight) {
      dyCorrection = constraints.maxHeight - potentialBounds.bottom;
    }

    // If corrections are needed, adjust the position
    if (dxCorrection != 0 || dyCorrection != 0) {
      final correctedVertices = potentialVertices
          .map((vertex) => vertex.translate(dxCorrection, dyCorrection))
          .toList();
      final correctedCenter =
          potentialCenter.translate(dxCorrection, dyCorrection);

      // Create corrected shape data
      final correctedShapeData = originalShapeData.copyWith(
        vertices: correctedVertices,
        center: correctedCenter,
      );

      // Recalculate bounds after correction
      final correctedBoundsResult =
          GeometryUtils.calculateAccurateBoundingRect(correctedShapeData);
      if (correctedBoundsResult is GroupBoundsData) {
        potentialBounds = correctedBoundsResult.bounds;
      } else {
        potentialBounds = correctedBoundsResult as Rect;
      }

      return correctedShapeData.copyWith(boundingRect: potentialBounds);
    }

    // Return the potential shape data with the updated bounds
    return tempShapeData.copyWith(boundingRect: potentialBounds);
  }

  // Helper method to update the UI for a shape after drag
  void _updateShapeUIAfterDrag(Key shapeKey, int selectedIndex) {
    // Find the shape in the list
    final index = shapes.indexWhere((s) => s.key == shapeKey);
    if (index < 0) return;

    // Get the updated shape data
    final updatedShapeData = shapeManager.shapeStates[shapeKey];
    if (updatedShapeData == null) return;

    // Create a new widget with the updated data
    final oldShape = shapes[index];
    final newShape = TransformableShape(
      key: shapeKey,
      constraints: oldShape.constraints,
      initialShapeType: oldShape.initialShapeType,
      initialRect: oldShape.initialRect,
      selected: true,
      initialShapeData: updatedShapeData,
      initialCurveMode: isCurveModeActive() &&
          selectedIndices.isNotEmpty &&
          selectedIndex == 0, // Only first selected shape gets curve mode
    );

    // Update the shape in the list
    shapes[index] = newShape;
    update(); // Trigger a rebuild
  }

  // --- Custom Shape Creation properties ---
  final RxList<Offset> customShapeVertices = <Offset>[].obs;
  final RxBool isCreatingCustomShape = false.obs;
  final RxBool shouldCloseCustomShape = false.obs;

  // --- Custom Shape History for undo/redo ---
  final RxList<List<Offset>> customShapeHistory = <List<Offset>>[].obs;
  final RxInt customShapeHistoryIndex = (-1).obs;

  // --- Custom Shape Vertex Interaction ---
  final RxInt selectedCustomShapeVertexIndex = (-1).obs;
  final RxBool isDraggingCustomShapeVertex = false.obs;
  final double customShapeVertexHitRadius =
      15.0; // Radius for vertex hit detection

  // --- Drag tracking for smooth movement ---
  Offset?
      _customShapeVertexDragStart; // Initial vertex position when drag starts
  Offset? _customShapePointerStart; // Initial pointer position when drag starts

  // --- Custom Shape Creation methods ---

  /// Start creating a custom shape by enabling the creation mode
  void startCustomShapeCreation() {
    if (isMirrorModeActive.value) {
      Get.snackbar(
        'Mirror Mode Active',
        'Custom shape creation is not available in mirror mode.',
        duration: const Duration(seconds: 2),
        snackPosition: SnackPosition.BOTTOM,
      );
      return;
    }

    isCreatingCustomShape.value = true;
    customShapeVertices.clear();
    shouldCloseCustomShape.value = false;

    // Initialize history
    customShapeHistory.clear();
    customShapeHistoryIndex.value = -1;
    selectedCustomShapeVertexIndex.value = -1;
    isDraggingCustomShapeVertex.value = false;

    // Clear drag tracking variables for clean start
    _customShapeVertexDragStart = null;
    _customShapePointerStart = null;

    // Deselect any selected shapes
    deselectAllShapes();

    // Start history tracking for this operation
    startHistoryTracking("Create Custom Shape");
  }

  /// Add a vertex to the custom shape history
  void _addCustomShapeVertexToHistory() {
    // Remove any history after the current index (when we're not at the end)
    if (customShapeHistoryIndex.value < customShapeHistory.length - 1) {
      customShapeHistory.removeRange(
          customShapeHistoryIndex.value + 1, customShapeHistory.length);
    }

    // Add current state to history
    customShapeHistory.add(List<Offset>.from(customShapeVertices));
    customShapeHistoryIndex.value = customShapeHistory.length - 1;

    // Limit history size to prevent memory issues
    if (customShapeHistory.length > 50) {
      customShapeHistory.removeAt(0);
      customShapeHistoryIndex.value--;
    }
  }

  /// Undo the last custom shape vertex addition
  void undoCustomShapeVertex() {
    if (!isCreatingCustomShape.value) return;

    if (customShapeHistoryIndex.value > 0) {
      customShapeHistoryIndex.value--;
      final previousState = customShapeHistory[customShapeHistoryIndex.value];
      customShapeVertices.assignAll(previousState);
      selectedCustomShapeVertexIndex.value = -1;
      shouldCloseCustomShape.value = false;

      // Add haptic feedback
      HapticFeedback.lightImpact();
    } else if (customShapeHistoryIndex.value == 0) {
      // Go to empty state
      customShapeHistoryIndex.value = -1;
      customShapeVertices.clear();
      selectedCustomShapeVertexIndex.value = -1;
      shouldCloseCustomShape.value = false;

      // Add haptic feedback
      HapticFeedback.lightImpact();
    }
  }

  /// Redo the next custom shape vertex addition
  void redoCustomShapeVertex() {
    if (!isCreatingCustomShape.value) return;

    if (customShapeHistoryIndex.value < customShapeHistory.length - 1) {
      customShapeHistoryIndex.value++;
      final nextState = customShapeHistory[customShapeHistoryIndex.value];
      customShapeVertices.assignAll(nextState);
      selectedCustomShapeVertexIndex.value = -1;
      shouldCloseCustomShape.value = false;

      // Add haptic feedback
      HapticFeedback.lightImpact();
    }
  }

  /// Check if we can undo custom shape vertices
  bool canUndoCustomShapeVertex() {
    return isCreatingCustomShape.value && customShapeHistoryIndex.value >= 0;
  }

  /// Check if we can redo custom shape vertices
  bool canRedoCustomShapeVertex() {
    return isCreatingCustomShape.value &&
        customShapeHistoryIndex.value < customShapeHistory.length - 1;
  }

  /// Handle tap on canvas during custom shape creation - supports both adding vertices and selecting existing ones
  void handleCustomShapeCanvasTap(Offset position) {
    if (!isCreatingCustomShape.value) return;

    // First check if we're tapping on an existing vertex
    final hitVertexIndex = _getHitCustomShapeVertex(position);

    if (hitVertexIndex != -1) {
      // Tapped on existing vertex - select it
      selectedCustomShapeVertexIndex.value = hitVertexIndex;
      isDraggingCustomShapeVertex.value = false;
      return;
    }

    // Check if tapping near the first vertex to close the shape
    if (customShapeVertices.isNotEmpty && customShapeVertices.length > 2) {
      final firstVertex = customShapeVertices.first;
      final distanceToFirst = (firstVertex - position).distance;

      if (distanceToFirst < 20) {
        shouldCloseCustomShape.value = true;
        selectedCustomShapeVertexIndex.value = -1;
        return;
      }
    }

    // Deselect any selected vertex
    selectedCustomShapeVertexIndex.value = -1;

    // Add new vertex
    addCustomShapeVertex(position);
  }

  /// Get the index of the vertex at the given position, or -1 if none
  int _getHitCustomShapeVertex(Offset position) {
    for (int i = 0; i < customShapeVertices.length; i++) {
      final vertex = customShapeVertices[i];
      final distance = (vertex - position).distance;
      if (distance <= customShapeVertexHitRadius) {
        return i;
      }
    }
    return -1;
  }

  /// Start dragging a custom shape vertex
  void startCustomShapeVertexDrag(int vertexIndex) {
    if (!isCreatingCustomShape.value ||
        vertexIndex < 0 ||
        vertexIndex >= customShapeVertices.length) return;

    selectedCustomShapeVertexIndex.value = vertexIndex;
    isDraggingCustomShapeVertex.value = true;
    shouldCloseCustomShape.value = false;

    // Store the initial vertex position for smooth dragging
    _customShapeVertexDragStart = customShapeVertices[vertexIndex];
  }

  /// Start tracking pointer position for drag calculation
  void startCustomShapeVertexDragWithPointer(
      int vertexIndex, Offset pointerPosition) {
    if (!isCreatingCustomShape.value ||
        vertexIndex < 0 ||
        vertexIndex >= customShapeVertices.length) return;

    selectedCustomShapeVertexIndex.value = vertexIndex;
    isDraggingCustomShapeVertex.value = true;
    shouldCloseCustomShape.value = false;

    // Store both initial positions for accurate delta calculation
    _customShapeVertexDragStart = customShapeVertices[vertexIndex];
    _customShapePointerStart = pointerPosition;
  }

  /// Update the position of a custom shape vertex during drag (smooth version)
  void updateCustomShapeVertexDragPosition(
      int vertexIndex, Offset pointerPosition) {
    if (!isCreatingCustomShape.value ||
        vertexIndex < 0 ||
        vertexIndex >= customShapeVertices.length ||
        !isDraggingCustomShapeVertex.value ||
        _customShapeVertexDragStart == null ||
        _customShapePointerStart == null) return;

    // Calculate the new position based on the delta from start positions
    final delta = pointerPosition - _customShapePointerStart!;
    final newPosition = _customShapeVertexDragStart! + delta;

    // Update the vertex position directly without snapping during drag
    customShapeVertices[vertexIndex] = newPosition;
  }

  /// Update the position of a custom shape vertex during drag
  void updateCustomShapeVertexPosition(int vertexIndex, Offset newPosition) {
    if (!isCreatingCustomShape.value ||
        vertexIndex < 0 ||
        vertexIndex >= customShapeVertices.length ||
        !isDraggingCustomShapeVertex.value) return;

    // For direct position updates (legacy method), just update without snapping
    customShapeVertices[vertexIndex] = newPosition;
  }

  /// End dragging a custom shape vertex
  void endCustomShapeVertexDrag() {
    if (!isDraggingCustomShapeVertex.value) return;

    // Apply grid snapping only at the end of the drag for the selected vertex
    if (selectedCustomShapeVertexIndex.value >= 0 &&
        selectedCustomShapeVertexIndex.value < customShapeVertices.length) {
      final Size size = Size(Get.width, Get.height);
      final currentPosition =
          customShapeVertices[selectedCustomShapeVertexIndex.value];
      final snappedPosition = snappingManager.snapPoint(currentPosition, size);

      // Update with snapped position
      customShapeVertices[selectedCustomShapeVertexIndex.value] =
          snappedPosition;

      // Add snap indicator for visual feedback if snapped
      if ((snappedPosition - currentPosition).distance > 0.5) {
        addSnapIndicator(snappedPosition);
      }
    }

    isDraggingCustomShapeVertex.value = false;

    // Clear drag tracking
    _customShapeVertexDragStart = null;
    _customShapePointerStart = null;

    // Add current state to history after dragging
    _addCustomShapeVertexToHistory();

    // Add haptic feedback
    HapticFeedback.lightImpact();
  }

  /// Move a custom shape vertex to a new position (used for programmatic moves)
  void moveCustomShapeVertex(int vertexIndex, Offset newPosition) {
    if (!isCreatingCustomShape.value ||
        vertexIndex < 0 ||
        vertexIndex >= customShapeVertices.length) return;

    // Apply grid snapping
    final Size size = Size(Get.width, Get.height);
    final snappedPosition = snappingManager.snapPoint(newPosition, size);

    // Update the vertex position
    customShapeVertices[vertexIndex] = snappedPosition;

    // Add to history
    _addCustomShapeVertexToHistory();

    // Add snap indicator for visual feedback
    if ((snappedPosition - newPosition).distance > 0.5) {
      addSnapIndicator(snappedPosition);
    }
  }

  /// Delete the currently selected custom shape vertex
  void deleteSelectedCustomShapeVertex() {
    if (!isCreatingCustomShape.value ||
        selectedCustomShapeVertexIndex.value < 0 ||
        selectedCustomShapeVertexIndex.value >= customShapeVertices.length)
      return;

    // Don't allow deleting if we have less than 3 vertices
    if (customShapeVertices.length <= 3) {
      Get.snackbar(
        'Cannot Delete',
        'A custom shape must have at least 3 vertices.',
        duration: const Duration(seconds: 2),
        snackPosition: SnackPosition.BOTTOM,
      );
      return;
    }

    customShapeVertices.removeAt(selectedCustomShapeVertexIndex.value);
    selectedCustomShapeVertexIndex.value = -1;
    shouldCloseCustomShape.value = false;

    // Add to history
    _addCustomShapeVertexToHistory();

    // Add haptic feedback
    HapticFeedback.mediumImpact();
  }

  /// Add a vertex to the custom shape (original method with history support)
  void addCustomShapeVertex(Offset position) {
    if (!isCreatingCustomShape.value) return;

    // Apply grid snapping if enabled
    final Size size = Size(Get.width, Get.height);
    final snappedPosition = snappingManager.snapPoint(position, size);

    // Add a snap indicator for visual feedback
    if ((snappedPosition - position).distance > 0.5) {
      addSnapIndicator(snappedPosition);
    }

    // Add the vertex to the list
    customShapeVertices.add(snappedPosition);

    // Deselect any selected vertex when adding new one
    selectedCustomShapeVertexIndex.value = -1;

    // Add to history
    _addCustomShapeVertexToHistory();

    // Add haptic feedback
    HapticFeedback.lightImpact();
  }

  /// Finish creating a custom shape and convert vertices to a ShapeData
  void finishCustomShapeCreation() {
    if (!isCreatingCustomShape.value || customShapeVertices.length < 3) return;

    // If needed, close the shape by connecting back to the first vertex
    final vertices = List<Offset>.from(customShapeVertices);

    // Calculate bounding box for the shape
    double minX = double.infinity;
    double minY = double.infinity;
    double maxX = -double.infinity;
    double maxY = -double.infinity;

    for (final vertex in vertices) {
      minX = math.min(minX, vertex.dx);
      minY = math.min(minY, vertex.dy);
      maxX = math.max(maxX, vertex.dx);
      maxY = math.max(maxY, vertex.dy);
    }

    final boundingRect = Rect.fromLTRB(minX, minY, maxX, maxY);
    final center = Offset(
      boundingRect.left + boundingRect.width / 2,
      boundingRect.top + boundingRect.height / 2,
    );

    // Create the shape data
    final customShapeData = ShapeData(
      type: ShapeType.custom,
      vertices: vertices,
      boundingRect: boundingRect,
      center: center,
      rotation: 0.0,
      visualRotation: 0.0,
    );

    // Add the shape to the board
    BoxConstraints constraints = BoxConstraints(
      maxWidth: Get.width,
      maxHeight: GridConstants.getExtendedHeight(Get.height),
    );

    // Create the TransformableShape
    final shape = TransformableShape(
      key: GlobalKey(),
      constraints: constraints,
      initialShapeType: ShapeType.custom,
      initialShapeData: customShapeData,
      selected: true,
    );

    // Add the shape to the controller
    final shapeKey = shape.key;
    if (shapeKey == null) {
      cancelCustomShapeCreation();
      return;
    }

    // Save shape state
    shapeManager.shapes.add(shape);
    shapeManager.shapeStates[shapeKey] = customShapeData;
    shapeManager.shapeKeysOrder.add(shapeKey);
    _uiStateManager.curveModeStates[shapeKey] = false;

    // If we've converted to grid coordinates, make sure this shape is also converted
    if (hasConvertedToGridCoordinates.value) {
      shapeManager.shapeStates[shapeKey] =
          customShapeData.toGridCoordinates(gridCoordinateConverter);
    }

    // Select the new shape
    selectedIndices.clear();
    int newIndex = shapes.indexWhere((s) => s.key == shapeKey);
    if (newIndex != -1) selectedIndices.add(newIndex);

    // Finish the custom shape creation
    cancelCustomShapeCreation();

    // Finish history tracking
    finishHistoryTracking();

    // Update the UI
    update();
  }

  /// Cancel the custom shape creation process
  void cancelCustomShapeCreation() {
    isCreatingCustomShape.value = false;
    customShapeVertices.clear();
    shouldCloseCustomShape.value = false;

    // Clear custom shape specific state
    customShapeHistory.clear();
    customShapeHistoryIndex.value = -1;
    selectedCustomShapeVertexIndex.value = -1;
    isDraggingCustomShapeVertex.value = false;

    // Clear drag tracking variables
    _customShapeVertexDragStart = null;
    _customShapePointerStart = null;

    // Instead of cancelHistoryTracking (which doesn't exist), we'll use existing methods
    // Just don't finish tracking, so the operation won't be recorded in history

    update();
  }

  // --- Vertex Edit Mode properties ---
  final RxBool isVertexEditModeActive = false.obs;
  final RxInt selectedVertexIndex = (-1).obs;
  final RxBool isDraggingVertex = false.obs;
  final double vertexHitRadius = 15.0; // Radius for vertex hit detection

  // --- Drag tracking for smooth vertex movement ---
  Offset? _vertexDragStart; // Initial vertex position when drag starts
  Offset? _vertexPointerStart; // Initial pointer position when drag starts
  Key? _editingShapeKey; // Key of the shape being edited

  // --- Vertex Edit Mode Methods ---

  /// Toggle vertex edit mode on/off
  void toggleVertexEditMode() {
    if (isMirrorModeActive.value) {
      Get.snackbar(
        'Mirror Mode Active',
        'Vertex edit mode is not available in mirror mode.',
        duration: const Duration(seconds: 2),
        snackPosition: SnackPosition.BOTTOM,
      );
      return;
    }

    // If turning off vertex edit mode, clear selection state
    if (isVertexEditModeActive.value) {
      selectedVertexIndex.value = -1;
      isDraggingVertex.value = false;
      _editingShapeKey = null;
      _vertexDragStart = null;
      _vertexPointerStart = null;
    }

    isVertexEditModeActive.value = !isVertexEditModeActive.value;
    update();
  }

  /// Check if a tap is on a vertex of the selected shape
  /// Returns the vertex index if hit, -1 if no vertex was hit
  int getHitVertexIndex(Offset tapPosition) {
    if (!isVertexEditModeActive.value || selectedIndices.isEmpty) return -1;

    // Get the first selected shape for vertex editing
    final selectedIndex = selectedIndices.first;
    if (selectedIndex < 0 || selectedIndex >= shapes.length) return -1;

    final selectedShape = shapes[selectedIndex];
    final shapeKey = selectedShape.key;
    if (shapeKey == null) return -1;

    final shapeData = shapeStates[shapeKey];
    if (shapeData == null) return -1;

    // Don't allow vertex editing on group shapes
    if (shapeData.type == ShapeType.group || shapeData is GroupShapeData) {
      return -1;
    }

    // Check each vertex for hit detection
    for (int i = 0; i < shapeData.vertices.length; i++) {
      final vertex = shapeData.vertices[i];
      final distance = (vertex - tapPosition).distance;
      if (distance <= vertexHitRadius) {
        _editingShapeKey = shapeKey;
        return i;
      }
    }

    return -1;
  }

  /// Handle tap on canvas during vertex edit mode
  void handleVertexEditCanvasTap(Offset tapPosition) {
    if (!isVertexEditModeActive.value) return;

    final hitVertexIndex = getHitVertexIndex(tapPosition);

    if (hitVertexIndex != -1) {
      // Tapped on a vertex - select it
      selectedVertexIndex.value = hitVertexIndex;
      isDraggingVertex.value = false;
    } else {
      // Tapped on background - deselect vertex
      selectedVertexIndex.value = -1;
      isDraggingVertex.value = false;
      _editingShapeKey = null;
    }
  }

  /// Start dragging a vertex
  void startVertexDrag(int vertexIndex, Offset pointerPosition) {
    if (!isVertexEditModeActive.value ||
        selectedIndices.isEmpty ||
        vertexIndex < 0) return;

    final selectedIndex = selectedIndices.first;
    if (selectedIndex < 0 || selectedIndex >= shapes.length) return;

    final selectedShape = shapes[selectedIndex];
    final shapeKey = selectedShape.key;
    if (shapeKey == null) return;

    final shapeData = shapeStates[shapeKey];
    if (shapeData == null || vertexIndex >= shapeData.vertices.length) return;

    // Don't allow vertex editing on group shapes
    if (shapeData.type == ShapeType.group || shapeData is GroupShapeData) {
      return;
    }

    selectedVertexIndex.value = vertexIndex;
    isDraggingVertex.value = true;
    _editingShapeKey = shapeKey;

    // Store initial positions for smooth dragging
    _vertexDragStart = shapeData.vertices[vertexIndex];
    _vertexPointerStart = pointerPosition;

    // Start history tracking for vertex edit
    startHistoryTracking("Edit Vertex");
  }

  /// Update vertex position during drag
  void updateVertexDragPosition(int vertexIndex, Offset pointerPosition) {
    if (!isVertexEditModeActive.value ||
        !isDraggingVertex.value ||
        _editingShapeKey == null ||
        _vertexDragStart == null ||
        _vertexPointerStart == null ||
        selectedIndices.isEmpty) return;

    final selectedIndex = selectedIndices.first;
    if (selectedIndex < 0 || selectedIndex >= shapes.length) return;

    final shapeData = shapeStates[_editingShapeKey];
    if (shapeData == null || vertexIndex >= shapeData.vertices.length) return;

    // Calculate the new position based on drag delta
    final delta = pointerPosition - _vertexPointerStart!;
    final newPosition = _vertexDragStart! + delta;

    // Update the vertex position in a copy of the shape data
    final updatedVertices = List<Offset>.from(shapeData.vertices);
    updatedVertices[vertexIndex] = newPosition;

    // Recalculate bounding box and center
    final updatedShapeData =
        _recalculateShapeDataAfterVertexEdit(shapeData, updatedVertices);

    // Update the shape state without triggering history yet
    shapeStates[_editingShapeKey!] = updatedShapeData;

    // Update the visual representation
    _updateShapeUIAfterVertexEdit(
        _editingShapeKey!, selectedIndex, updatedShapeData);
  }

  /// End vertex drag and apply grid snapping
  void endVertexDrag() {
    if (!isDraggingVertex.value || _editingShapeKey == null) return;

    // Apply grid snapping to the final position
    if (selectedVertexIndex.value >= 0) {
      final shapeData = shapeStates[_editingShapeKey];
      if (shapeData != null &&
          selectedVertexIndex.value < shapeData.vertices.length) {
        final currentPosition = shapeData.vertices[selectedVertexIndex.value];
        final size = Size(Get.width, Get.height);
        final snappedPosition =
            snappingManager.snapPoint(currentPosition, size);

        // Update with snapped position if different
        if ((snappedPosition - currentPosition).distance > 0.5) {
          final updatedVertices = List<Offset>.from(shapeData.vertices);
          updatedVertices[selectedVertexIndex.value] = snappedPosition;

          final updatedShapeData =
              _recalculateShapeDataAfterVertexEdit(shapeData, updatedVertices);

          // Save the final snapped state
          saveShapeState(_editingShapeKey!, updatedShapeData);

          // Add snap indicator for visual feedback
          addSnapIndicator(snappedPosition);
        }
      }
    }

    isDraggingVertex.value = false;
    _vertexDragStart = null;
    _vertexPointerStart = null;

    // Clear active handle state
    clearActiveHandle();

    // Finish history tracking
    finishHistoryTracking();

    // Add haptic feedback
    HapticFeedback.lightImpact();
  }

  /// Recalculate shape data after vertex editing
  ShapeData _recalculateShapeDataAfterVertexEdit(
      ShapeData originalData, List<Offset> newVertices) {
    // Calculate new bounding box
    double minX = double.infinity;
    double minY = double.infinity;
    double maxX = -double.infinity;
    double maxY = -double.infinity;

    for (final vertex in newVertices) {
      minX = math.min(minX, vertex.dx);
      minY = math.min(minY, vertex.dy);
      maxX = math.max(maxX, vertex.dx);
      maxY = math.max(maxY, vertex.dy);
    }

    final newBoundingRect = Rect.fromLTRB(minX, minY, maxX, maxY);
    final newCenter = Offset(
      newBoundingRect.left + newBoundingRect.width / 2,
      newBoundingRect.top + newBoundingRect.height / 2,
    );

    return originalData.copyWith(
      vertices: newVertices,
      boundingRect: newBoundingRect,
      center: newCenter,
    );
  }

  /// Update shape UI after vertex edit
  void _updateShapeUIAfterVertexEdit(
      Key shapeKey, int shapeIndex, ShapeData updatedData) {
    if (shapeIndex < 0 || shapeIndex >= shapes.length) return;

    final oldShape = shapes[shapeIndex];
    final newShape = TransformableShape(
      key: shapeKey,
      constraints: oldShape.constraints,
      initialShapeType: oldShape.initialShapeType,
      initialRect: oldShape.initialRect,
      selected: true,
      initialShapeData: updatedData,
      initialCurveMode: isCurveModeActive() && selectedIndices.isNotEmpty,
    );

    shapes[shapeIndex] = newShape;
    update();
  }

  /// Get the vertices of the currently selected shape for vertex edit mode
  List<Offset>? getSelectedShapeVertices() {
    if (!isVertexEditModeActive.value || selectedIndices.isEmpty) return null;

    final selectedIndex = selectedIndices.first;
    if (selectedIndex < 0 || selectedIndex >= shapes.length) return null;

    final selectedShape = shapes[selectedIndex];
    final shapeKey = selectedShape.key;
    if (shapeKey == null) return null;

    final shapeData = shapeStates[shapeKey];
    if (shapeData == null) return null;

    // Don't return vertices for group shapes
    if (shapeData.type == ShapeType.group || shapeData is GroupShapeData) {
      return null;
    }

    return shapeData.vertices;
  }

  // NEW: Handle mode transition methods

  /// Enter side editing mode for the specified shape
  void enterSideEditingMode(Key shapeKey) {
    if (isMirrorModeActive.value) {
      Get.snackbar(
        'Mirror Mode Active',
        'Side editing is not available in mirror mode.',
        duration: const Duration(seconds: 2),
        snackPosition: SnackPosition.BOTTOM,
      );
      return;
    }

    // Exit any existing modes first
    exitCurrentHandleMode();

    // Set the new mode
    currentHandleMode.value = HandleMode.sideEdit;
    activeHandleModeShapeKey.value = shapeKey;

    // Ensure the shape is selected and is the only selected shape
    // Find the target shape index first
    final shapeIndex = shapes.indexWhere((shape) => shape.key == shapeKey);
    if (shapeIndex == -1) {
      debugPrint(
          '[HandleMode] Cannot enter side editing mode: shape not found');
      return;
    }

    // Only modify selection if needed to avoid flickering
    if (selectedIndices.length != 1 || selectedIndices.first != shapeIndex) {
      selectedIndices.clear();
      selectedIndices.add(shapeIndex);
      selectionManager.updateShapeSelectionState(
          curveModeStates: _uiStateManager.curveModeStates);
    }

    // Convert any legacy quadratic curves to cubic curves for this shape
    _convertShapeQuadraticToCubic(shapeKey);

    // Initialize default control handle spacing if needed
    _initializeDefaultCubicControlSpacing(shapeKey);

    // Force immediate UI update
    update();

    debugPrint('[HandleMode] Entered side editing mode for shape: $shapeKey');
  }

  /// Enter point editing mode for the specified shape
  void enterPointEditingMode(Key shapeKey) {
    if (isMirrorModeActive.value) {
      Get.snackbar(
        'Mirror Mode Active',
        'Point editing is not available in mirror mode.',
        duration: const Duration(seconds: 2),
        snackPosition: SnackPosition.BOTTOM,
      );
      return;
    }

    // Exit any existing modes first
    exitCurrentHandleMode();

    // Set the new mode
    currentHandleMode.value = HandleMode.pointEdit;
    activeHandleModeShapeKey.value = shapeKey;

    // Ensure the shape is selected and is the only selected shape
    // Find the target shape index first
    final shapeIndex = shapes.indexWhere((shape) => shape.key == shapeKey);
    if (shapeIndex == -1) {
      debugPrint(
          '[HandleMode] Cannot enter point editing mode: shape not found');
      return;
    }

    // Only modify selection if needed to avoid flickering
    if (selectedIndices.length != 1 || selectedIndices.first != shapeIndex) {
      selectedIndices.clear();
      selectedIndices.add(shapeIndex);
      selectionManager.updateShapeSelectionState(
          curveModeStates: _uiStateManager.curveModeStates);
    }

    // Force immediate UI update
    update();

    debugPrint('[HandleMode] Entered point editing mode for shape: $shapeKey');
    debugPrint('[HandleMode] Current handle mode: ${currentHandleMode.value}');
  }

  /// Exit the current handle mode and return to normal mode
  void exitCurrentHandleMode() {
    if (currentHandleMode.value != HandleMode.normal) {
      final previousMode = currentHandleMode.value;
      debugPrint('[HandleMode] Exiting $previousMode mode');

      // Clear snap-to-straight-line states when exiting edge edit mode
      if (previousMode == HandleMode.sideEdit) {
        clearAllSnapToStraightStates();
      }

      currentHandleMode.value = HandleMode.normal;
      activeHandleModeShapeKey.value = null;

      // Clear active handle state when exiting modes
      clearActiveHandle();

      // Force immediate UI update to prevent handle flickering
      update();

      // Also schedule a post-frame callback for additional reliability
      WidgetsBinding.instance.addPostFrameCallback((_) {
        update();
      });

      debugPrint(
          '[HandleMode] Exited to normal mode, current mode: ${currentHandleMode.value}');
    }
  }

  /// Check if a specific shape is in the active handle mode
  bool isShapeInActiveMode(Key shapeKey) {
    return activeHandleModeShapeKey.value == shapeKey &&
        currentHandleMode.value != HandleMode.normal;
  }

  /// Get the current handle mode for a specific shape
  HandleMode getShapeHandleMode(Key shapeKey) {
    if (activeHandleModeShapeKey.value == shapeKey) {
      return currentHandleMode.value;
    }
    return HandleMode.normal;
  }

  /// Convert legacy quadratic curves to cubic curves for a shape
  void _convertShapeQuadraticToCubic(Key shapeKey) {
    final shapeData = shapeManager.shapeStates[shapeKey];
    if (shapeData == null || shapeData.curveControls.isEmpty) return;

    final newCubicControls = <int, List<Offset>>{};

    // Convert each quadratic control to cubic controls
    for (final entry in shapeData.curveControls.entries) {
      if (entry.value != Offset.zero) {
        final edgeIndex = entry.key;
        final quadraticOffset = entry.value;

        // Convert single quadratic control point to two cubic control points
        // Place them at appropriate positions for smooth transition
        final control1 = quadraticOffset * 0.6; // First control point
        final control2 = quadraticOffset * 0.6; // Second control point

        newCubicControls[edgeIndex] = [control1, control2];
      }
    }

    // Update the shape with cubic controls and clear legacy controls
    final updatedShapeData = shapeData.copyWith(
      cubicCurveControls: newCubicControls,
      curveControls: <int, Offset>{}, // Clear legacy controls
    );

    saveShapeState(shapeKey, updatedShapeData);
    debugPrint(
        '[HandleMode] Converted quadratic to cubic curves for shape: $shapeKey');
  }

  /// Handle long press on side editing activation handle
  void handleSideEditingActivationLongPress(Key shapeKey) {
    debugPrint(
        '[HandleMode] Side editing activation long press for shape: $shapeKey');

    // Provide immediate haptic feedback
    HapticFeedback.mediumImpact();

    // Enter the mode
    enterSideEditingMode(shapeKey);

    // Force immediate UI update to ensure handles appear
    WidgetsBinding.instance.addPostFrameCallback((_) {
      update();
    });
  }

  /// Handle long press on point editing activation handle
  void handlePointEditingActivationLongPress(Key shapeKey) {
    debugPrint(
        '[HandleMode] Point editing activation long press for shape: $shapeKey');

    // Provide immediate haptic feedback
    HapticFeedback.mediumImpact();

    // Enter the mode
    enterPointEditingMode(shapeKey);

    // Force immediate UI update to ensure handles appear
    WidgetsBinding.instance.addPostFrameCallback((_) {
      update();
    });
  }

  /// Update cubic curve control point for a shape edge
  void updateCubicCurveControl(
      Key shapeKey, int edgeIndex, int controlIndex, Offset newControlOffset) {
    final shapeData = shapeManager.shapeStates[shapeKey];
    if (shapeData == null) return;

    // Initialize curve controls for this edge if it's currently straight
    // This ensures edges only become curved when explicitly manipulated
    _initializeCurveControlsForEdge(shapeKey, edgeIndex);

    // Get the current controls (which may have been just initialized)
    final updatedShapeData = shapeManager.shapeStates[shapeKey];
    if (updatedShapeData == null) return;

    final currentControls = updatedShapeData.getEdgeCubicControls(edgeIndex);
    final newControls = List<Offset>.from(currentControls);

    if (controlIndex >= 0 && controlIndex < 2) {
      newControls[controlIndex] = newControlOffset;

      final finalShapeData = updatedShapeData.setEdgeCubicControls(
          edgeIndex, newControls[0], newControls[1]);
      saveShapeState(shapeKey, finalShapeData);
    }
  }

  /// Get the snap-to-straight-line threshold based on current zoom level
  double getSnapToStraightLineThreshold() {
    // Base threshold in grid units (pixels at 1.0 zoom)
    const double baseThreshold = 8.0;

    // Adjust threshold based on zoom level - smaller threshold when zoomed in
    // This ensures consistent behavior regardless of zoom level
    return baseThreshold / zoomScale.value;
  }

  /// Track snap-to-straight-line state for visual feedback
  final Map<String, bool> _edgeSnapToStraightStates = {};

  /// Set the snap-to-straight-line state for an edge
  void setEdgeSnapToStraightState(
      Key shapeKey, int edgeIndex, bool isSnapping) {
    final stateKey = '${shapeKey.toString()}_$edgeIndex';
    if (isSnapping) {
      _edgeSnapToStraightStates[stateKey] = true;
    } else {
      _edgeSnapToStraightStates.remove(stateKey);
    }
    // Trigger UI update for visual feedback
    update();
  }

  /// Check if an edge is in snap-to-straight-line state
  bool isEdgeSnappingToStraight(Key shapeKey, int edgeIndex) {
    final stateKey = '${shapeKey.toString()}_$edgeIndex';
    return _edgeSnapToStraightStates[stateKey] ?? false;
  }

  /// Clear all snap-to-straight-line states (called when exiting edge edit mode)
  void clearAllSnapToStraightStates() {
    _edgeSnapToStraightStates.clear();
    update();
  }

  /// Initialize default spacing for cubic control handles when entering side edit mode
  /// This method now only ensures the cubic controls map exists but does NOT
  /// automatically initialize curve controls for straight edges
  void _initializeDefaultCubicControlSpacing(Key shapeKey) {
    final shapeData = shapeManager.shapeStates[shapeKey];
    if (shapeData == null) return;

    // Only ensure that the cubic controls map exists for the shape
    // Do NOT automatically initialize curve controls for all edges
    // This preserves straight edges until the user explicitly curves them

    // Check if we need to initialize the cubic controls map at all
    if (shapeData.cubicCurveControls.isEmpty) {
      // Initialize an empty cubic controls map to ensure the shape can handle
      // cubic curve operations when needed, but don't add any actual curves
      final emptyControls = <int, List<Offset>>{};
      final updatedShapeData = shapeData.copyWith(
        cubicCurveControls: emptyControls,
      );
      saveShapeState(shapeKey, updatedShapeData);
      debugPrint(
          '[HandleMode] Initialized empty cubic controls map for shape: $shapeKey');
    }

    debugPrint(
        '[HandleMode] Shape ready for edge editing without auto-curving edges');
  }

  /// Initialize curve controls for a specific edge when user starts manipulating it
  /// This is called only when the user actually drags a curve control handle
  void _initializeCurveControlsForEdge(Key shapeKey, int edgeIndex) {
    final shapeData = shapeManager.shapeStates[shapeKey];
    if (shapeData == null) return;

    final currentControls = shapeData.getEdgeCubicControls(edgeIndex);

    // Only initialize if both control points are at zero (edge is currently straight)
    if (currentControls[0] == Offset.zero &&
        currentControls[1] == Offset.zero) {
      final startVertex = shapeData.vertices[edgeIndex];
      final endVertex =
          shapeData.vertices[(edgeIndex + 1) % shapeData.vertices.length];
      final edgeVector = endVertex - startVertex;
      final edgeLength = edgeVector.distance;

      // Create default control offsets perpendicular to the edge
      // Use a smaller percentage for more subtle initial curves
      final defaultOffset = edgeLength * 0.05; // Reduced from 0.1 to 0.05
      final perpendicularVector = Offset(-edgeVector.dy, edgeVector.dx);
      final perpendicular = edgeLength > 0
          ? perpendicularVector / edgeLength * defaultOffset
          : Offset.zero;

      // Position control points with minimal offset to start
      final newControls = [
        perpendicular * 0.2, // Very small positive offset
        perpendicular * -0.2, // Very small negative offset
      ];

      final updatedShapeData = shapeData.setEdgeCubicControls(
          edgeIndex, newControls[0], newControls[1]);
      saveShapeState(shapeKey, updatedShapeData);

      debugPrint(
          '[HandleMode] Initialized curve controls for edge $edgeIndex on first manipulation');
    }
  }
}
